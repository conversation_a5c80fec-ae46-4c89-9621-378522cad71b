package com.nrb.dms.utils

import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.content.res.Resources
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class DeviceTypeUtilTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockPackageManager: PackageManager

    @Mock
    private lateinit var mockResources: Resources

    @Mock
    private lateinit var mockConfiguration: Configuration

    @Before
    fun setup() {
        `when`(mockContext.packageManager).thenReturn(mockPackageManager)
        `when`(mockContext.resources).thenReturn(mockResources)
        `when`(mockResources.configuration).thenReturn(mockConfiguration)
    }

    @Test
    fun testGetDeviceType_whenTablet_returnsTablet() {
        // Set up mock to simulate a tablet (large screen)
        mockConfiguration.screenLayout = Configuration.SCREENLAYOUT_SIZE_LARGE
        `when`(mockPackageManager.hasSystemFeature(PackageManager.FEATURE_SENSOR_HINGE_ANGLE)).thenReturn(false)

        // Test
        val result = DeviceTypeUtil.getDeviceType(mockContext)

        // Verify
        assertEquals(DeviceTypeUtil.DeviceType.TABLET, result)
    }

    @Test
    fun testGetDeviceType_whenPhone_returnsPhone() {
        // Set up mock to simulate a phone (normal screen)
        mockConfiguration.screenLayout = Configuration.SCREENLAYOUT_SIZE_NORMAL
        `when`(mockPackageManager.hasSystemFeature(PackageManager.FEATURE_SENSOR_HINGE_ANGLE)).thenReturn(false)

        // Test
        val result = DeviceTypeUtil.getDeviceType(mockContext)

        // Verify
        assertEquals(DeviceTypeUtil.DeviceType.PHONE, result)
    }

    @Test
    fun testGetDeviceType_whenFoldable_returnsFoldable() {
        // Set up mock to simulate a foldable device (has hinge sensor)
        `when`(mockPackageManager.hasSystemFeature(PackageManager.FEATURE_SENSOR_HINGE_ANGLE)).thenReturn(true)

        // Test
        val result = DeviceTypeUtil.getDeviceType(mockContext)

        // Verify
        assertEquals(DeviceTypeUtil.DeviceType.FOLDABLE, result)
    }

    @Test
    fun testGetDeviceTypeString_returnsCorrectString() {
        // Set up mock to simulate a tablet
        mockConfiguration.screenLayout = Configuration.SCREENLAYOUT_SIZE_LARGE
        `when`(mockPackageManager.hasSystemFeature(PackageManager.FEATURE_SENSOR_HINGE_ANGLE)).thenReturn(false)

        // Test
        val result = DeviceTypeUtil.getDeviceTypeString(mockContext)

        // Verify
        assertEquals("tablet", result)
    }
}
