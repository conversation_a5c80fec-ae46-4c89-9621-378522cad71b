/**
 * DMS Discovery Helper
 * 
 * This utility class helps other apps discover and verify if the DMS app is installed
 * and properly configured on the device.
 */

package com.yourapp.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ProviderInfo
import android.net.Uri
import android.util.Log

class DMSDiscoveryHelper(private val context: Context) {
    
    companion object {
        private const val TAG = "DMSDiscoveryHelper"
        private const val DMS_PACKAGE = "com.nrb.dms"
        private const val DMS_PROVIDER_AUTHORITY = "com.nrb.dms.provider"
        private const val DMS_ACTION = "com.nrb.dms.action.DEVICE_MANAGEMENT"
        private const val DMS_PERMISSION = "com.nrb.dms.permission.READ_DEVICE_ID"
    }
    
    /**
     * Check if DMS app is installed on the device
     */
    fun isDMSAppInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo(DMS_PACKAGE, 0)
            Log.d(TAG, "DMS app is installed")
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "DMS app is not installed")
            false
        }
    }
    
    /**
     * Check if DMS ContentProvider is available and accessible
     */
    fun isDMSProviderAvailable(): Boolean {
        return try {
            val providerInfo = context.packageManager.resolveContentProvider(
                DMS_PROVIDER_AUTHORITY, 
                PackageManager.GET_META_DATA
            )
            
            if (providerInfo != null) {
                Log.d(TAG, "DMS ContentProvider is available")
                Log.d(TAG, "Provider package: ${providerInfo.packageName}")
                Log.d(TAG, "Provider authority: ${providerInfo.authority}")
                Log.d(TAG, "Provider exported: ${providerInfo.exported}")
                true
            } else {
                Log.e(TAG, "DMS ContentProvider not found")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking DMS ContentProvider", e)
            false
        }
    }
    
    /**
     * Check if the current app has permission to access DMS data
     */
    fun hasPermissionToAccessDMS(): Boolean {
        val permission = context.checkCallingOrSelfPermission(DMS_PERMISSION)
        val hasPermission = permission == PackageManager.PERMISSION_GRANTED
        
        Log.d(TAG, "Permission $DMS_PERMISSION: ${if (hasPermission) "GRANTED" else "DENIED"}")
        return hasPermission
    }
    
    /**
     * Test if DMS ContentProvider is responding
     */
    fun testDMSConnection(): Boolean {
        if (!isDMSAppInstalled()) {
            Log.e(TAG, "Cannot test connection: DMS app not installed")
            return false
        }
        
        if (!hasPermissionToAccessDMS()) {
            Log.e(TAG, "Cannot test connection: Missing permission")
            return false
        }
        
        return try {
            val uri = Uri.parse("content://$DMS_PROVIDER_AUTHORITY/device_id")
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            
            cursor?.use {
                val hasData = it.count > 0
                Log.d(TAG, "DMS ContentProvider test: ${if (hasData) "SUCCESS" else "NO DATA"}")
                hasData
            } ?: false
        } catch (e: Exception) {
            Log.e(TAG, "DMS ContentProvider test failed", e)
            false
        }
    }
    
    /**
     * Launch DMS app to ensure it's initialized
     */
    fun launchDMSApp(): Boolean {
        return try {
            // Try to launch with custom action first
            val customIntent = Intent(DMS_ACTION).apply {
                setPackage(DMS_PACKAGE)
            }
            
            if (context.packageManager.resolveActivity(customIntent, 0) != null) {
                context.startActivity(customIntent)
                Log.d(TAG, "Launched DMS app with custom action")
                true
            } else {
                // Fallback to launcher intent
                val launcherIntent = context.packageManager.getLaunchIntentForPackage(DMS_PACKAGE)
                if (launcherIntent != null) {
                    context.startActivity(launcherIntent)
                    Log.d(TAG, "Launched DMS app with launcher intent")
                    true
                } else {
                    Log.e(TAG, "Cannot launch DMS app: No suitable intent found")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error launching DMS app", e)
            false
        }
    }
    
    /**
     * Get comprehensive DMS status
     */
    fun getDMSStatus(): DMSStatus {
        return DMSStatus(
            isAppInstalled = isDMSAppInstalled(),
            isProviderAvailable = isDMSProviderAvailable(),
            hasPermission = hasPermissionToAccessDMS(),
            isConnectionWorking = testDMSConnection()
        )
    }
    
    /**
     * Get installation instructions for missing components
     */
    fun getInstallationInstructions(): String {
        val status = getDMSStatus()
        val instructions = StringBuilder()
        
        if (!status.isAppInstalled) {
            instructions.append("1. Install the DMS app (package: $DMS_PACKAGE)\n")
        }
        
        if (!status.hasPermission) {
            instructions.append("2. Add permission to your AndroidManifest.xml:\n")
            instructions.append("   <uses-permission android:name=\"$DMS_PERMISSION\" />\n")
        }
        
        if (status.isAppInstalled && !status.isConnectionWorking) {
            instructions.append("3. Launch the DMS app at least once to initialize it\n")
        }
        
        return if (instructions.isEmpty()) {
            "✓ DMS is properly configured and ready to use!"
        } else {
            "DMS Setup Required:\n$instructions"
        }
    }
}

/**
 * Data class representing DMS status
 */
data class DMSStatus(
    val isAppInstalled: Boolean,
    val isProviderAvailable: Boolean,
    val hasPermission: Boolean,
    val isConnectionWorking: Boolean
) {
    val isFullyConfigured: Boolean
        get() = isAppInstalled && isProviderAvailable && hasPermission && isConnectionWorking
        
    override fun toString(): String {
        return """
            DMS Status:
            - App Installed: ${if (isAppInstalled) "✓" else "✗"}
            - Provider Available: ${if (isProviderAvailable) "✓" else "✗"}
            - Permission Granted: ${if (hasPermission) "✓" else "✗"}
            - Connection Working: ${if (isConnectionWorking) "✓" else "✗"}
            - Fully Configured: ${if (isFullyConfigured) "✓" else "✗"}
        """.trimIndent()
    }
}

/**
 * Example usage:
 * 
 * val dmsHelper = DMSDiscoveryHelper(context)
 * val status = dmsHelper.getDMSStatus()
 * 
 * if (status.isFullyConfigured) {
 *     // Use DMS services
 *     val deviceHelper = DMSDeviceIdHelper(context)
 *     val deviceId = deviceHelper.getDeviceId()
 * } else {
 *     // Show setup instructions
 *     val instructions = dmsHelper.getInstallationInstructions()
 *     Log.d("Setup", instructions)
 *     
 *     // Optionally launch DMS app
 *     dmsHelper.launchDMSApp()
 * }
 */
