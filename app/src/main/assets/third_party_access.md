# Accessing Device ID from Third-Party Apps

This document explains how third-party apps can access the device ID from the DMS app.

**Note: The Device ID in the DMS system is the Android ID of the device. This provides a unique, persistent identifier for each device.**

## Prerequisites

1. **The DMS app must be installed on the device** - This is the most common cause of integration issues
2. **No permissions required** - The ContentProvider is configured for open access to any app regardless of signature

## Common Error: "DMS app with package 'com.nrb.dms' is not installed"

If you see this error, it means the DMS app is not installed on the device. Follow these steps:

1. **Install the DMS app APK:**
   ```bash
   adb install path/to/dms-app.apk
   ```

2. **Launch the DMS app** at least once to initialize it

3. **Verify installation:**
   ```bash
   adb shell pm list packages | grep com.nrb.dms
   ```

4. **Check if the ContentProvider is accessible:**
   ```bash
   adb shell content query --uri content://com.nrb.dms.provider/device_id
   ```

## Important Notes

1. Make sure you're using the exact authority string `com.nrb.dms.provider` when accessing the ContentProvider. The full URI must be `content://com.nrb.dms.provider/device_id`.

2. The DMS app must be installed on the device before attempting to access the ContentProvider.

3. Your app must declare and be granted the `com.nrb.dms.permission.READ_DEVICE_ID` permission.

4. The device must be registered with the DMS system for the device ID to be available.

## Step 1: Add the Permission to Your Manifest

Add the following permission to your app's `AndroidManifest.xml`:

```xml
<uses-permission android:name="com.nrb.dms.permission.READ_DEVICE_ID" />
```

## Step 2: Check for Permission

Before accessing the device ID, check if your app has the required permission:

```kotlin
val permission = "com.nrb.dms.permission.READ_DEVICE_ID"
val hasPermission = context.checkCallingOrSelfPermission(permission) == PackageManager.PERMISSION_GRANTED

if (!hasPermission) {
    // Handle missing permission
    return
}
```

## Step 3: Access the Device ID

Use the ContentProvider to access the device ID:

```kotlin
try {
    // Make sure to use the exact authority string
    val uri = Uri.parse("content://com.nrb.dms.provider/device_id")
    val cursor = context.contentResolver.query(uri, null, null, null, null)

    cursor?.use {
        if (it.moveToFirst()) {
            val deviceIdIndex = it.getColumnIndex("device_id")
            val registeredIndex = it.getColumnIndex("registered")

            if (deviceIdIndex != -1 && registeredIndex != -1) {
                val isRegistered = it.getInt(registeredIndex) == 1

                if (isRegistered) {
                    val deviceId = it.getString(deviceIdIndex)  // This IS the Android ID

                    // Use the device ID (which is the Android ID)
                    Log.d("DMS", "Device ID (Android ID): $deviceId")
                } else {
                    Log.d("DMS", "Device is not registered yet")
                }
            }
        }
    }
} catch (e: Exception) {
    Log.e("DMS", "Error accessing DMS ContentProvider", e)
    // Check if the DMS app is installed
    val dmsPackageName = "com.nrb.dms"
    try {
        context.packageManager.getPackageInfo(dmsPackageName, 0)
        Log.d("DMS", "DMS app is installed but ContentProvider access failed")
    } catch (e: PackageManager.NameNotFoundException) {
        Log.e("DMS", "DMS app is not installed")
    }
}
```

## Step 4: Using the Helper Class (Recommended)

For convenience, we provide a sample helper class that you can copy to your project. This class handles all the common tasks and error cases:

```kotlin
// Create the helper
val dmsHelper = DMSDeviceIdHelper(context)

// Check if DMS app is installed
if (dmsHelper.isDmsAppInstalled()) {
    // Check if we have permission
    if (dmsHelper.hasPermission()) {
        // Check if device is registered
        if (dmsHelper.isDeviceRegistered()) {
            // Get the device ID
            val deviceId = dmsHelper.getDeviceId()
            if (deviceId != null) {
                // Use the device ID
                Log.d("MyApp", "Device ID: $deviceId")
            }

            // Or get all device information at once
            val deviceInfo = dmsHelper.getDeviceInfo()
            deviceInfo?.let {
                val deviceType = it["device_type"] as String
                val deviceName = it["device_name"] as String
                // Use the device information
            }
        } else {
            // Device is not registered
            // Prompt user to open DMS app and complete registration
        }
    } else {
        // No permission
        // Inform user they need to grant permission
    }
} else {
    // DMS app not installed
    // Prompt user to install DMS app
}
```

The complete helper class is available in the DMS app's assets folder as `third_party_sample_code.kt`. You can copy this file to your project and modify it as needed.

## Available Data

The ContentProvider exposes the following data:

| Column | Type | Description |
|--------|------|-------------|
| device_id | String | The unique device ID |
| registered | Integer | 1 if registered, 0 if not |
| timestamp | Long | Registration timestamp in milliseconds |
| device_type | String | Type of device (phone, tablet, foldable) |

## Error Handling and Troubleshooting

Always handle potential errors when accessing the ContentProvider:

1. Check if the DMS app is installed
2. Check if your app has the required permission
3. Handle cases where the device is not registered
4. Use try-catch blocks to handle ContentProvider exceptions

### Common Issues and Solutions

#### "Failed to find provider info for com.nrb.dms.provider"

This error typically occurs when:

- The DMS app is not installed on the device
- The ContentProvider authority is misspelled or incorrect
- The DMS app has not been launched at least once after installation

**Solution:**
- Verify the DMS app is installed
- Double-check the authority string: it must be exactly `com.nrb.dms.provider`
- Launch the DMS app at least once before attempting to access the ContentProvider

#### Permission Denied

This error occurs when your app doesn't have the required permission.

**Solution:**
- Make sure you've declared the permission in your AndroidManifest.xml
- For Android 6.0+ (API level 23+), ensure you've requested the permission at runtime if needed

#### Empty Cursor or No Device ID

This occurs when the device hasn't been registered with the DMS system yet.

**Solution:**
- Check the `registered` column value (should be 1 for registered devices)
- Inform the user that they need to complete the registration process in the DMS app

## Security Considerations

- The device ID should be treated as sensitive information
- Do not share the device ID with third parties without user consent
- Store the device ID securely within your app
- Clear the device ID when it's no longer needed
