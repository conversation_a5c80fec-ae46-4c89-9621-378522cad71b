# DMS App Installation and Integration Guide

## Overview
This guide helps you install the DMS app and integrate it with your client applications.

## Prerequisites
- Android device with API level 24 or higher
- ADB (Android Debug Bridge) installed on your development machine
- DMS app APK file

## Installation Steps

### Step 1: Build the DMS App (if needed)
If you have the source code:
```bash
cd /path/to/DMS/project
./gradlew assembleDebug
```
The APK will be generated at: `app/build/outputs/apk/debug/app-debug.apk`

### Step 2: Install the DMS App
```bash
# Install via ADB
adb install app-debug.apk

# Or install via Android Studio
# File -> Open -> Select DMS project -> Run
```

### Step 3: Launch and Initialize DMS App
1. Open the DMS app on your device
2. Complete the initial setup/registration process
3. Ensure the device is registered with the DMS system

### Step 4: Verify Installation
Use this code in your client app to verify the DMS app is properly installed:

```kotlin
val helper = DMSDeviceIdHelper(this)
val status = helper.getDMSInstallationStatus()
Log.d("DMS_CHECK", status)

if (helper.isDmsAppInstalled()) {
    Log.d("DMS_CHECK", "DMS app is available")
    if (helper.hasPermission()) {
        val deviceId = helper.getDeviceId()  // This IS the Android ID
        Log.d("DMS_CHECK", "Device ID (Android ID): $deviceId")
    } else {
        Log.e("DMS_CHECK", "Missing permission: com.nrb.dms.permission.READ_DEVICE_ID")
    }
} else {
    Log.e("DMS_CHECK", "DMS app is not installed")
}
```

## Troubleshooting

### Error: "DMS app with package 'com.nrb.dms' is not installed"

**Cause:** The DMS app is not installed on the device.

**Solutions:**
1. Install the DMS app APK on the device
2. Verify the package name is correct: `com.nrb.dms`
3. Check if the app was uninstalled or disabled

### Error: "Failed to find provider info for com.nrb.dms.provider"

**Cause:** The ContentProvider is not accessible.

**Solutions:**
1. Ensure the DMS app is installed and launched at least once
2. Check that the DMS app has the ContentProvider properly declared
3. Verify the client app has the required permission

### Error: "Missing permission com.nrb.dms.permission.READ_DEVICE_ID"

**Cause:** The client app doesn't have the required permission.

**Solution:** Add this to your client app's AndroidManifest.xml:
```xml
<uses-permission android:name="com.nrb.dms.permission.READ_DEVICE_ID" />
```

### Error: "Cursor is null" or "Cursor is empty"

**Cause:** The device is not registered in the DMS system.

**Solutions:**
1. Open the DMS app and complete device registration
2. Check network connectivity for registration
3. Verify the DMS server is accessible

## Client App Integration

You can integrate with DMS in two ways:

### Method 1: Broadcast Listener (Recommended - Works Even When Not Registered)

The DMS app broadcasts the device ID (Android ID) whenever it starts, regardless of registration status.

#### 1. Add Broadcast Receiver
Copy the `DMSBroadcastListener` class from the sample code to your project.

#### 2. Register the Listener
```kotlin
class MainActivity : AppCompatActivity() {
    private val dmsBroadcastListener = object : DMSBroadcastListener() {
        override fun onDMSAvailable(deviceId: String?, providerAuthority: String?, isRegistered: Boolean) {
            // Got device ID immediately - works even if not registered!
            Log.d("MyApp", "Device ID: $deviceId, Registered: $isRegistered")
            handleDeviceId(deviceId)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DMSBroadcastListener.register(this, dmsBroadcastListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        DMSBroadcastListener.unregister(this, dmsBroadcastListener)
    }
}
```

### Method 2: ContentProvider Access (Open Access - Any App Signature)

The DMS ContentProvider is configured with open access, allowing any app to access it regardless of signing certificate.

#### 1. Copy the Helper Class
Copy the `DMSDeviceIdHelper` class from the sample code to your project.

#### 3. Use the Helper Class
```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var dmsHelper: DMSDeviceIdHelper
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        dmsHelper = DMSDeviceIdHelper(this)
        loadDeviceInfo()
    }
    
    private fun loadDeviceInfo() {
        // Check if DMS app is installed
        if (!dmsHelper.isDmsAppInstalled()) {
            showError("DMS app is not installed. Please install it first.")
            return
        }
        
        // Check permission
        if (!dmsHelper.hasPermission()) {
            showError("Missing permission to access DMS data.")
            return
        }
        
        // Get device information
        val deviceId = dmsHelper.getDeviceId()
        if (deviceId != null) {
            showDeviceInfo(deviceId)
        } else {
            showError("Device is not registered in DMS system.")
        }
    }
    
    private fun showDeviceInfo(deviceId: String) {
        // Display device information
        Log.d("MainActivity", "Device ID: $deviceId")
    }
    
    private fun showError(message: String) {
        Log.e("MainActivity", message)
        // Show error to user
    }
}
```

## Verification Commands

### Check if DMS app is installed:
```bash
adb shell pm list packages | grep com.nrb.dms
```

### Check ContentProvider:
```bash
adb shell content query --uri content://com.nrb.dms.provider/device_id
```

### Check permissions:
```bash
adb shell dumpsys package com.nrb.dms | grep permission
```

## Support
If you continue to experience issues:
1. Check the device logs: `adb logcat | grep DMS`
2. Verify the DMS app version and compatibility
3. Ensure both apps are signed with compatible certificates (for production)
