/**
 * Comprehensive DMS Diagnostic Tool
 * 
 * This will find exactly what's wrong with the DMS detection
 */

package com.nrb.dmstest

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.ProviderInfo
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "DMSDiagnostic"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        Log.d(TAG, "🔍 COMPREHENSIVE DMS DIAGNOSTIC STARTING...")
        Log.d(TAG, "=" * 80)
        
        runDiagnostic()
    }
    
    private fun runDiagnostic() {
        // Test 1: Find ALL packages with "dms" in name
        findDMSPackages()
        
        // Test 2: Check specific package existence with different methods
        checkPackageExistence()
        
        // Test 3: Find ALL ContentProviders
        findAllContentProviders()
        
        // Test 4: Test ContentProvider access with all possible URIs
        testContentProviderAccess()
        
        // Test 5: Check app visibility and permissions
        checkAppVisibility()
        
        Log.d(TAG, "=" * 80)
        Log.d(TAG, "🔍 DIAGNOSTIC COMPLETE - Check results above")
    }
    
    private fun findDMSPackages() {
        Log.d(TAG, "TEST 1: Finding ALL packages containing 'dms'")
        
        try {
            val pm = packageManager
            val installedPackages = pm.getInstalledPackages(PackageManager.GET_META_DATA)
            
            Log.d(TAG, "Total packages installed: ${installedPackages.size}")
            
            val dmsPackages = installedPackages.filter { 
                it.packageName.contains("dms", ignoreCase = true) 
            }
            
            if (dmsPackages.isNotEmpty()) {
                Log.d(TAG, "✅ Found ${dmsPackages.size} packages with 'dms':")
                dmsPackages.forEach { pkg ->
                    Log.d(TAG, "  📦 ${pkg.packageName}")
                    Log.d(TAG, "     Version: ${pkg.versionName} (code: ${pkg.versionCode})")
                    Log.d(TAG, "     Target SDK: ${pkg.applicationInfo?.targetSdkVersion}")
                    
                    // Check if app is enabled
                    try {
                        val appInfo = pm.getApplicationInfo(pkg.packageName, 0)
                        Log.d(TAG, "     Enabled: ${appInfo.enabled}")
                        Log.d(TAG, "     System app: ${(appInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0}")
                    } catch (e: Exception) {
                        Log.d(TAG, "     Status: Cannot determine")
                    }
                }
            } else {
                Log.e(TAG, "❌ NO packages found containing 'dms'!")
                Log.e(TAG, "This means the DMS app is either:")
                Log.e(TAG, "1. Not installed")
                Log.e(TAG, "2. Has a completely different package name")
                Log.e(TAG, "3. Not visible to this app")
            }
            
            // Also check for 'nrb' packages
            val nrbPackages = installedPackages.filter { 
                it.packageName.contains("nrb", ignoreCase = true) 
            }
            
            if (nrbPackages.isNotEmpty()) {
                Log.d(TAG, "✅ Found ${nrbPackages.size} packages with 'nrb':")
                nrbPackages.forEach { pkg ->
                    Log.d(TAG, "  📦 ${pkg.packageName}")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error scanning packages", e)
        }
    }
    
    private fun checkPackageExistence() {
        Log.d(TAG, "TEST 2: Checking specific package 'com.nrb.dms' with different methods")
        
        val targetPackage = "com.nrb.dms"
        
        // Method 1: getPackageInfo
        try {
            val packageInfo = packageManager.getPackageInfo(targetPackage, 0)
            Log.d(TAG, "✅ Method 1 (getPackageInfo): SUCCESS")
            Log.d(TAG, "   Package: ${packageInfo.packageName}")
            Log.d(TAG, "   Version: ${packageInfo.versionName}")
            Log.d(TAG, "   Install time: ${java.util.Date(packageInfo.firstInstallTime)}")
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "❌ Method 1 (getPackageInfo): Package not found")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Method 1 (getPackageInfo): Error - ${e.message}")
        }
        
        // Method 2: getApplicationInfo
        try {
            val appInfo = packageManager.getApplicationInfo(targetPackage, 0)
            Log.d(TAG, "✅ Method 2 (getApplicationInfo): SUCCESS")
            Log.d(TAG, "   Enabled: ${appInfo.enabled}")
            Log.d(TAG, "   Data dir: ${appInfo.dataDir}")
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "❌ Method 2 (getApplicationInfo): Package not found")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Method 2 (getApplicationInfo): Error - ${e.message}")
        }
        
        // Method 3: getLaunchIntentForPackage
        try {
            val launchIntent = packageManager.getLaunchIntentForPackage(targetPackage)
            if (launchIntent != null) {
                Log.d(TAG, "✅ Method 3 (getLaunchIntent): SUCCESS")
                Log.d(TAG, "   Launch intent: $launchIntent")
            } else {
                Log.e(TAG, "❌ Method 3 (getLaunchIntent): No launch intent found")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Method 3 (getLaunchIntent): Error - ${e.message}")
        }
        
        // Method 4: Check in installed packages list
        try {
            val installedPackages = packageManager.getInstalledPackages(0)
            val found = installedPackages.any { it.packageName == targetPackage }
            if (found) {
                Log.d(TAG, "✅ Method 4 (installedPackages list): FOUND")
            } else {
                Log.e(TAG, "❌ Method 4 (installedPackages list): NOT FOUND")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Method 4 (installedPackages list): Error - ${e.message}")
        }
    }
    
    private fun findAllContentProviders() {
        Log.d(TAG, "TEST 3: Finding ALL ContentProviders")
        
        try {
            // Method 1: Query all ContentProviders
            val allProviders = packageManager.queryContentProviders(null, 0, PackageManager.GET_META_DATA)
            Log.d(TAG, "Total ContentProviders found: ${allProviders?.size ?: 0}")
            
            // Look for DMS-related providers
            val dmsProviders = allProviders?.filter { provider ->
                provider.authority?.contains("dms", ignoreCase = true) == true ||
                provider.packageName?.contains("dms", ignoreCase = true) == true ||
                provider.authority?.contains("nrb", ignoreCase = true) == true ||
                provider.packageName?.contains("nrb", ignoreCase = true) == true
            }
            
            if (dmsProviders?.isNotEmpty() == true) {
                Log.d(TAG, "✅ Found ${dmsProviders.size} DMS-related ContentProviders:")
                dmsProviders.forEach { provider ->
                    Log.d(TAG, "  🔗 Authority: ${provider.authority}")
                    Log.d(TAG, "     Package: ${provider.packageName}")
                    Log.d(TAG, "     Exported: ${provider.exported}")
                    Log.d(TAG, "     Read permission: ${provider.readPermission ?: "None"}")
                    Log.d(TAG, "     Write permission: ${provider.writePermission ?: "None"}")
                }
            } else {
                Log.e(TAG, "❌ NO DMS-related ContentProviders found!")
            }
            
            // Method 2: Check specific authority
            val specificAuthority = "com.nrb.dms.provider"
            val providerInfo = packageManager.resolveContentProvider(specificAuthority, PackageManager.GET_META_DATA)
            if (providerInfo != null) {
                Log.d(TAG, "✅ Specific authority '$specificAuthority' found:")
                Log.d(TAG, "   Package: ${providerInfo.packageName}")
                Log.d(TAG, "   Exported: ${providerInfo.exported}")
                Log.d(TAG, "   Enabled: ${providerInfo.enabled}")
            } else {
                Log.e(TAG, "❌ Specific authority '$specificAuthority' NOT found")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error scanning ContentProviders", e)
        }
    }
    
    private fun testContentProviderAccess() {
        Log.d(TAG, "TEST 4: Testing ContentProvider Access")
        
        val testUris = listOf(
            "content://com.nrb.dms.provider/device_id",
            "content://com.nrb.dms.provider/",
            "content://com.nrb.dms.provider"
        )
        
        testUris.forEach { uriString ->
            Log.d(TAG, "Testing URI: $uriString")
            
            try {
                val uri = Uri.parse(uriString)
                Log.d(TAG, "  Parsed URI: $uri")
                Log.d(TAG, "  Authority: ${uri.authority}")
                Log.d(TAG, "  Path: ${uri.path}")
                
                val cursor = contentResolver.query(uri, null, null, null, null)
                
                if (cursor != null) {
                    Log.d(TAG, "  ✅ SUCCESS! Got cursor")
                    Log.d(TAG, "  Cursor count: ${cursor.count}")
                    Log.d(TAG, "  Column count: ${cursor.columnCount}")
                    
                    if (cursor.columnCount > 0) {
                        Log.d(TAG, "  Columns: ${cursor.columnNames.joinToString(", ")}")
                        
                        if (cursor.moveToFirst()) {
                            Log.d(TAG, "  Sample data:")
                            for (i in 0 until minOf(cursor.columnCount, 5)) { // Show max 5 columns
                                val columnName = cursor.getColumnName(i)
                                val value = try {
                                    cursor.getString(i)
                                } catch (e: Exception) {
                                    "[Error reading: ${e.message}]"
                                }
                                Log.d(TAG, "    $columnName: $value")
                            }
                        }
                    }
                    cursor.close()
                } else {
                    Log.e(TAG, "  ❌ Cursor is null")
                }
                
            } catch (e: SecurityException) {
                Log.e(TAG, "  ❌ SecurityException: ${e.message}")
            } catch (e: IllegalArgumentException) {
                Log.e(TAG, "  ❌ IllegalArgumentException: ${e.message}")
            } catch (e: Exception) {
                Log.e(TAG, "  ❌ Exception: ${e.javaClass.simpleName}: ${e.message}")
            }
        }
    }
    
    private fun checkAppVisibility() {
        Log.d(TAG, "TEST 5: Checking App Visibility and Permissions")
        
        try {
            // Check target SDK version
            val myAppInfo = packageManager.getApplicationInfo(packageName, 0)
            Log.d(TAG, "Client app target SDK: ${myAppInfo.targetSdkVersion}")
            
            // Check if we can see system apps
            val systemApps = packageManager.getInstalledPackages(0).filter { pkg ->
                (pkg.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
            }
            Log.d(TAG, "Can see ${systemApps.size} system apps")
            
            // Check specific permissions
            val permissions = listOf(
                "android.permission.QUERY_ALL_PACKAGES",
                "com.nrb.dms.permission.READ_DEVICE_ID"
            )
            
            permissions.forEach { permission ->
                val result = checkCallingOrSelfPermission(permission)
                val status = if (result == PackageManager.PERMISSION_GRANTED) "GRANTED" else "DENIED"
                Log.d(TAG, "Permission $permission: $status")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking app visibility", e)
        }
    }
}

/**
 * INSTRUCTIONS:
 * 
 * 1. Replace your client app's MainActivity with this code
 * 2. Run the app
 * 3. Check the logs with filter "DMSDiagnostic"
 * 4. Share the complete log output
 * 
 * This will tell us EXACTLY what's wrong with the DMS detection.
 */
