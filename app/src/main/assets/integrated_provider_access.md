# DMS Integrated ContentProvider Access Guide

This guide explains how third-party apps can access device registration information and store custom data using the DMS (Device Management System) app's integrated ContentProvider system.

## Overview

The DMS app now provides a modern, integrated ContentProvider system that combines:

1. **Device Registration Data** - Stored as standardized key-value pairs
2. **Generic Key-Value Storage** - For custom app data alongside device information
3. **Multiple Access Methods** - Helper classes and direct ContentProvider access

## System Architecture

```
DMS Registration System
         ↓
   DeviceDataManager (stores device data as key-value pairs)
         ↓
   DataContentProvider (generic key-value storage)
         ↓
   Third-party apps (via DeviceRegistrationHelper or direct access)
```

## ContentProvider Details

- **Authority**: `com.nrb.dms.provider`
- **Base URI**: `content://com.nrb.dms.provider/data`
- **Access**: Open to all apps (no permissions required)
- **Data Format**: Key-value pairs with timestamps
- **Storage**: SQLite database with full CRUD operations

## Device Data Keys

When a device is registered, the following standardized keys are stored:

| Key | Description | Example Value |
|-----|-------------|---------------|
| `device_id` | Android ID | `abc123def456` |
| `registered` | Registration status | `true` |
| `registration_timestamp` | Registration time | `1691234567890` |
| `device_type` | Device category | `phone`, `tablet`, `foldable` |
| `device_name` | Device name | `SM-G991B` |
| `device_model` | Device model | `Galaxy S21` |
| `manufacturer` | Manufacturer | `Samsung` |
| `android_version` | Android version | `13` |
| `api_level` | API level | `33` |
| `brand` | Brand | `samsung` |
| `product` | Product | `o1sxxx` |
| `hardware` | Hardware | `exynos2100` |
| `fingerprint` | Build fingerprint | `samsung/o1sxxx/...` |
| `locale` | Device locale | `en_US` |
| `timezone` | Device timezone | `America/New_York` |

## Access Methods

### Method 1: DeviceRegistrationHelper (Recommended)

The easiest way to access device registration data:

```kotlin
val helper = DeviceRegistrationHelper(context)

// Check if device is registered
if (helper.isDeviceRegistered()) {
    val deviceId = helper.getDeviceId()
    val deviceType = helper.getDeviceType()
    val timestamp = helper.getRegistrationTimestamp()
    
    // Use device information
    Log.d("DMS", "Device ID: $deviceId, Type: $deviceType")
}
```

### Method 2: Generic DataContentProvider

For custom data storage or specific key queries:

```kotlin
// Query a specific key
val uri = Uri.parse("content://com.nrb.dms.provider/data/device_id")
val cursor = contentResolver.query(uri, null, null, null, null)

cursor?.use {
    if (it.moveToFirst()) {
        val value = it.getString(it.getColumnIndex("value"))
        val timestamp = it.getLong(it.getColumnIndex("timestamp"))
        Log.d("DMS", "Device ID: $value (stored at: $timestamp)")
    }
}
```

### Method 3: Direct ContentProvider Access

For advanced queries or bulk operations:

```kotlin
// Query all data
val uri = Uri.parse("content://com.nrb.dms.provider/data")
val cursor = contentResolver.query(uri, null, null, null, null)

cursor?.use {
    while (it.moveToNext()) {
        val key = it.getString(it.getColumnIndex("key"))
        val value = it.getString(it.getColumnIndex("value"))
        val timestamp = it.getLong(it.getColumnIndex("timestamp"))
        
        if (key.startsWith("device_")) {
            Log.d("DMS", "Device data: $key = $value")
        }
    }
}
```

## Custom Data Storage

Third-party apps can also store their own data in the same ContentProvider:

```kotlin
// Store custom app data
val values = ContentValues().apply {
    put("key", "my_app_setting")
    put("value", "custom_value")
}

val uri = contentResolver.insert(
    Uri.parse("content://com.nrb.dms.provider/data"),
    values
)
```

## Error Handling

Always handle potential errors when accessing the ContentProvider:

```kotlin
try {
    val helper = DeviceRegistrationHelper(context)
    if (helper.isDeviceRegistered()) {
        // Use device data
    } else {
        // Device not registered yet
        Log.w("DMS", "Device is not registered")
    }
} catch (e: Exception) {
    Log.e("DMS", "Error accessing DMS data", e)
    
    // Check if DMS app is installed
    try {
        context.packageManager.getPackageInfo("com.nrb.dms", 0)
        Log.d("DMS", "DMS app is installed but ContentProvider access failed")
    } catch (e: PackageManager.NameNotFoundException) {
        Log.e("DMS", "DMS app is not installed")
    }
}
```

## Prerequisites

1. **DMS app must be installed** on the device
2. **No special permissions required** - ContentProvider is open access
3. **Device should be registered** for device data to be available

## Installation Verification

```bash
# Check if DMS app is installed
adb shell pm list packages | grep com.nrb.dms

# Test ContentProvider access
adb shell content query --uri content://com.nrb.dms.provider/data

# Query specific device data
adb shell content query --uri content://com.nrb.dms.provider/data/device_id
```

## Migration from Old System

If you were using the previous device-specific ContentProvider:

**Old URI**: `content://com.nrb.dms.provider/device_id`
**New URI**: `content://com.nrb.dms.provider/data/device_id`

**Old approach**:
```kotlin
// Old way - direct ContentProvider query
val cursor = contentResolver.query(
    Uri.parse("content://com.nrb.dms.provider/device_id"),
    null, null, null, null
)
```

**New approach**:
```kotlin
// New way - use DeviceRegistrationHelper
val helper = DeviceRegistrationHelper(context)
val deviceId = helper.getDeviceId()
```

## Complete Example

See `IntegratedProviderExample.kt` in the DMS app source code for a comprehensive example showing all access methods.
