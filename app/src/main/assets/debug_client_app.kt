/**
 * Debug Client App - Comprehensive DMS Detection
 * 
 * This version provides extensive debugging to find out exactly what's happening
 * with the DMS app detection and ContentProvider access.
 */

package com.nrb.dmstest

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.content.pm.ProviderInfo
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "DMSDebugClient"
        private const val DMS_PACKAGE = "com.nrb.dms"
        private const val DMS_AUTHORITY = "com.nrb.dms.provider"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        Log.d(TAG, "=".repeat(60))
        Log.d(TAG, "DMS DEBUG CLIENT STARTED")
        Log.d(TAG, "=".repeat(60))
        
        // Comprehensive debugging
        step1_CheckDeviceInfo()
        step2_FindAllDMSPackages()
        step3_CheckSpecificDMSPackage()
        step4_CheckContentProviders()
        step5_TestContentProviderAccess()
        step6_SetupBroadcastListener()
        step7_TryLaunchDMS()
        step8_GetDirectAndroidId()
        
        Log.d(TAG, "=".repeat(60))
        Log.d(TAG, "DEBUG COMPLETE - Check logs above")
        Log.d(TAG, "=".repeat(60))
    }
    
    private fun step1_CheckDeviceInfo() {
        Log.d(TAG, "STEP 1: Device Information")
        Log.d(TAG, "Android Version: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
        Log.d(TAG, "Device: ${Build.MANUFACTURER} ${Build.MODEL}")
        Log.d(TAG, "Client Package: ${packageName}")
    }
    
    private fun step2_FindAllDMSPackages() {
        Log.d(TAG, "STEP 2: Searching for ALL DMS-related packages")
        
        try {
            val allPackages = packageManager.getInstalledPackages(PackageManager.GET_META_DATA)
            Log.d(TAG, "Total installed packages: ${allPackages.size}")
            
            val dmsPackages = allPackages.filter { pkg ->
                pkg.packageName.contains("dms", ignoreCase = true) ||
                pkg.packageName.contains("nrb", ignoreCase = true) ||
                pkg.packageName.contains("device", ignoreCase = true)
            }
            
            if (dmsPackages.isNotEmpty()) {
                Log.d(TAG, "Found ${dmsPackages.size} potentially related packages:")
                dmsPackages.forEach { pkg ->
                    Log.d(TAG, "  📦 ${pkg.packageName}")
                    Log.d(TAG, "     Version: ${pkg.versionName} (${pkg.versionCode})")
                    Log.d(TAG, "     Installed: ${java.util.Date(pkg.firstInstallTime)}")
                    
                    // Check if it has our ContentProvider
                    try {
                        val appInfo = packageManager.getApplicationInfo(pkg.packageName, PackageManager.GET_META_DATA)
                        Log.d(TAG, "     Enabled: ${appInfo.enabled}")
                    } catch (e: Exception) {
                        Log.d(TAG, "     Status: Unknown")
                    }
                }
            } else {
                Log.w(TAG, "❌ NO DMS-related packages found!")
                Log.w(TAG, "This suggests the DMS app is not installed or has a different package name")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning packages", e)
        }
    }
    
    private fun step3_CheckSpecificDMSPackage() {
        Log.d(TAG, "STEP 3: Checking specific DMS package: $DMS_PACKAGE")
        
        try {
            val packageInfo = packageManager.getPackageInfo(DMS_PACKAGE, PackageManager.GET_META_DATA)
            Log.d(TAG, "✅ FOUND DMS APP!")
            Log.d(TAG, "  Package: ${packageInfo.packageName}")
            Log.d(TAG, "  Version: ${packageInfo.versionName} (${packageInfo.versionCode})")
            Log.d(TAG, "  First Install: ${java.util.Date(packageInfo.firstInstallTime)}")
            Log.d(TAG, "  Last Update: ${java.util.Date(packageInfo.lastUpdateTime)}")
            
            val appInfo = packageManager.getApplicationInfo(DMS_PACKAGE, PackageManager.GET_META_DATA)
            Log.d(TAG, "  Enabled: ${appInfo.enabled}")
            Log.d(TAG, "  Data Dir: ${appInfo.dataDir}")
            
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "❌ DMS package '$DMS_PACKAGE' NOT FOUND")
            Log.e(TAG, "Possible reasons:")
            Log.e(TAG, "1. DMS app not installed on this device")
            Log.e(TAG, "2. DMS app has different package name")
            Log.e(TAG, "3. DMS app installed but not accessible")
        } catch (e: Exception) {
            Log.e(TAG, "Error checking DMS package", e)
        }
    }
    
    private fun step4_CheckContentProviders() {
        Log.d(TAG, "STEP 4: Scanning for DMS ContentProvider")
        
        try {
            // Method 1: Check specific authority
            val providerInfo = packageManager.resolveContentProvider(DMS_AUTHORITY, PackageManager.GET_META_DATA)
            if (providerInfo != null) {
                Log.d(TAG, "✅ FOUND DMS ContentProvider!")
                Log.d(TAG, "  Authority: ${providerInfo.authority}")
                Log.d(TAG, "  Package: ${providerInfo.packageName}")
                Log.d(TAG, "  Exported: ${providerInfo.exported}")
                Log.d(TAG, "  Read Permission: ${providerInfo.readPermission ?: "None"}")
                Log.d(TAG, "  Write Permission: ${providerInfo.writePermission ?: "None"}")
            } else {
                Log.e(TAG, "❌ DMS ContentProvider NOT FOUND with authority: $DMS_AUTHORITY")
            }
            
            // Method 2: Scan all ContentProviders
            Log.d(TAG, "Scanning ALL ContentProviders for DMS...")
            val allProviders = packageManager.queryContentProviders(null, 0, PackageManager.GET_META_DATA)
            val dmsProviders = allProviders?.filter { provider ->
                provider.authority?.contains("dms", ignoreCase = true) == true ||
                provider.packageName?.contains("dms", ignoreCase = true) == true ||
                provider.packageName?.contains("nrb", ignoreCase = true) == true
            }
            
            if (dmsProviders?.isNotEmpty() == true) {
                Log.d(TAG, "Found ${dmsProviders.size} DMS-related ContentProviders:")
                dmsProviders.forEach { provider ->
                    Log.d(TAG, "  🔗 Authority: ${provider.authority}")
                    Log.d(TAG, "     Package: ${provider.packageName}")
                    Log.d(TAG, "     Exported: ${provider.exported}")
                }
            } else {
                Log.w(TAG, "No DMS-related ContentProviders found")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning ContentProviders", e)
        }
    }
    
    private fun step5_TestContentProviderAccess() {
        Log.d(TAG, "STEP 5: Testing ContentProvider Access")
        
        val testUris = listOf(
            "content://com.nrb.dms.provider/device_id",
            "content://com.nrb.dms.provider/",
            "content://com.nrb.dms.provider"
        )
        
        testUris.forEach { uriString ->
            Log.d(TAG, "Testing URI: $uriString")
            try {
                val uri = Uri.parse(uriString)
                val cursor = contentResolver.query(uri, null, null, null, null)
                
                cursor?.use {
                    Log.d(TAG, "  ✅ SUCCESS! Cursor obtained")
                    Log.d(TAG, "  Rows: ${it.count}")
                    Log.d(TAG, "  Columns: ${it.columnCount}")
                    if (it.columnCount > 0) {
                        Log.d(TAG, "  Column names: ${it.columnNames.joinToString(", ")}")
                    }
                    
                    if (it.moveToFirst()) {
                        Log.d(TAG, "  Data available:")
                        for (i in 0 until it.columnCount) {
                            val columnName = it.getColumnName(i)
                            val value = try {
                                when (it.getType(i)) {
                                    Cursor.FIELD_TYPE_STRING -> it.getString(i)
                                    Cursor.FIELD_TYPE_INTEGER -> it.getInt(i).toString()
                                    Cursor.FIELD_TYPE_FLOAT -> it.getFloat(i).toString()
                                    Cursor.FIELD_TYPE_BLOB -> "[BLOB]"
                                    Cursor.FIELD_TYPE_NULL -> "[NULL]"
                                    else -> it.getString(i)
                                }
                            } catch (e: Exception) {
                                "[ERROR: ${e.message}]"
                            }
                            Log.d(TAG, "    $columnName: $value")
                        }
                    } else {
                        Log.d(TAG, "  No data in cursor")
                    }
                } ?: run {
                    Log.e(TAG, "  ❌ Cursor is null")
                }
            } catch (e: SecurityException) {
                Log.e(TAG, "  ❌ SecurityException: ${e.message}")
            } catch (e: Exception) {
                Log.e(TAG, "  ❌ Exception: ${e.message}")
            }
        }
    }
    
    private fun step6_SetupBroadcastListener() {
        Log.d(TAG, "STEP 6: Setting up Broadcast Listener")
        
        try {
            val filter = IntentFilter().apply {
                addAction("com.nrb.dms.action.DMS_AVAILABLE")
                addAction("com.nrb.dms.action.DEVICE_REGISTERED")
            }
            
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    Log.d(TAG, "🔔 BROADCAST RECEIVED!")
                    Log.d(TAG, "  Action: ${intent.action}")
                    Log.d(TAG, "  Extras:")
                    intent.extras?.keySet()?.forEach { key ->
                        val value = intent.extras?.get(key)
                        Log.d(TAG, "    $key: $value")
                    }
                }
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
                Log.d(TAG, "✅ Broadcast receiver registered (EXPORTED)")
            } else {
                registerReceiver(receiver, filter)
                Log.d(TAG, "✅ Broadcast receiver registered (legacy)")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to setup broadcast listener", e)
        }
    }
    
    private fun step7_TryLaunchDMS() {
        Log.d(TAG, "STEP 7: Attempting to Launch DMS App")
        
        try {
            // Try launcher intent
            val launcherIntent = packageManager.getLaunchIntentForPackage(DMS_PACKAGE)
            if (launcherIntent != null) {
                Log.d(TAG, "Found launcher intent for DMS app")
                Log.d(TAG, "Intent: $launcherIntent")
                
                // Don't actually launch to avoid interrupting debugging
                Log.d(TAG, "✅ DMS app can be launched (not launching to avoid interruption)")
            } else {
                Log.e(TAG, "❌ No launcher intent found for DMS app")
            }
            
            // Try custom action
            val customIntent = Intent("com.nrb.dms.action.DEVICE_MANAGEMENT")
            customIntent.setPackage(DMS_PACKAGE)
            val resolveInfo = packageManager.resolveActivity(customIntent, 0)
            if (resolveInfo != null) {
                Log.d(TAG, "✅ Custom DMS action is available")
            } else {
                Log.d(TAG, "❌ Custom DMS action not available")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking DMS launch options", e)
        }
    }
    
    private fun step8_GetDirectAndroidId() {
        Log.d(TAG, "STEP 8: Getting Direct Android ID (Fallback)")
        
        try {
            val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
            Log.d(TAG, "✅ Direct Android ID: $androidId")
            Log.d(TAG, "This is what DMS should provide as Device ID")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to get Android ID", e)
        }
    }
}
