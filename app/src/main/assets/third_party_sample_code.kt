/**
 * Sample code for third-party apps to access the DMS ContentProvider
 * Copy this code to your app and modify as needed
 */
class DMSDeviceIdHelper(private val context: Context) {

    companion object {
        private const val TAG = "DMSDeviceIdHelper"
        private const val DMS_PACKAGE = "com.nrb.dms"
        private const val PERMISSION = "com.nrb.dms.permission.READ_DEVICE_ID"
        private const val PROVIDER_URI = "content://com.nrb.dms.provider/device_id"
        
        // Column names in the ContentProvider
        private const val COLUMN_DEVICE_ID = "device_id"  // This IS the Android ID
        private const val COLUMN_REGISTERED = "registered"
        private const val COLUMN_TIMESTAMP = "timestamp"
        private const val COLUMN_DEVICE_TYPE = "device_type"
        private const val COLUMN_DEVICE_NAME = "device_name"
    }
    
    /**
     * Check if the DMS app is installed
     */
    fun isDmsAppInstalled(): <PERSON><PERSON><PERSON> {
        return try {
            context.packageManager.getPackageInfo(DMS_PACKAGE, 0)
            Log.d(TAG, "DMS app is installed")
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "DMS app with package '$DMS_PACKAGE' is not installed", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if DMS app is installed", e)
            false
        }
    }

    /**
     * Get detailed installation status
     */
    fun getDMSInstallationStatus(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(DMS_PACKAGE, 0)
            "DMS app is installed (version: ${packageInfo.versionName}, code: ${packageInfo.versionCode})"
        } catch (e: PackageManager.NameNotFoundException) {
            "DMS app with package '$DMS_PACKAGE' is not installed. Please install the DMS app first."
        } catch (e: Exception) {
            "Error checking DMS app installation: ${e.message}"
        }
    }
    
    /**
     * Check if the app has permission to access the DMS ContentProvider
     * @return true (always granted - no permission required for Device ID access)
     */
    fun hasPermission(): Boolean {
        // No permission required since Device ID is just Android ID
        return true
    }
    


    /**
     * Get the device ID from the DMS app (which is the Android ID)
     * @return The device ID (Android ID) or null if not available
     */
    fun getDeviceId(): String? {
        // Check prerequisites
        if (!isDmsAppInstalled()) {
            Log.e(TAG, "Cannot get device ID: DMS app is not installed")
            return null
        }
        
        if (!hasPermission()) {
            Log.e(TAG, "Cannot get device ID: Missing permission $PERMISSION")
            return null
        }
        
        try {
            Log.d(TAG, "Querying ContentProvider at $PROVIDER_URI")
            val uri = Uri.parse(PROVIDER_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val deviceIdIndex = it.getColumnIndex(COLUMN_DEVICE_ID)
                    val registeredIndex = it.getColumnIndex(COLUMN_REGISTERED)
                    
                    if (deviceIdIndex != -1 && registeredIndex != -1) {
                        val isRegistered = it.getInt(registeredIndex) == 1
                        
                        if (isRegistered) {
                            val deviceId = it.getString(deviceIdIndex)
                            Log.d(TAG, "Successfully retrieved device ID: $deviceId")
                            return deviceId
                        } else {
                            Log.w(TAG, "Device is not registered in DMS")
                            return null
                        }
                    } else {
                        Log.e(TAG, "Required columns not found in cursor")
                        return null
                    }
                } else {
                    Log.e(TAG, "Cursor is empty")
                    return null
                }
            } ?: run {
                Log.e(TAG, "Failed to query ContentProvider: cursor is null")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing DMS ContentProvider", e)
            return null
        }
    }
    
    /**
     * Get all device information from the DMS app
     * @return A map of device information or null if not available
     */
    fun getDeviceInfo(): Map<String, Any>? {
        // Check prerequisites
        if (!isDmsAppInstalled()) {
            Log.e(TAG, "Cannot get device info: DMS app is not installed")
            return null
        }
        
        if (!hasPermission()) {
            Log.e(TAG, "Cannot get device info: Missing permission $PERMISSION")
            return null
        }
        
        try {
            val uri = Uri.parse(PROVIDER_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val result = mutableMapOf<String, Any>()
                    
                    // Get all column indices
                    val deviceIdIndex = it.getColumnIndex(COLUMN_DEVICE_ID)
                    val registeredIndex = it.getColumnIndex(COLUMN_REGISTERED)
                    val timestampIndex = it.getColumnIndex(COLUMN_TIMESTAMP)
                    val deviceTypeIndex = it.getColumnIndex(COLUMN_DEVICE_TYPE)
                    val deviceNameIndex = it.getColumnIndex(COLUMN_DEVICE_NAME)

                    // Add values to the result map if columns exist
                    if (deviceIdIndex != -1) {
                        result[COLUMN_DEVICE_ID] = it.getString(deviceIdIndex)  // This IS the Android ID
                    }

                    if (registeredIndex != -1) {
                        result[COLUMN_REGISTERED] = it.getInt(registeredIndex) == 1
                    }

                    if (timestampIndex != -1) {
                        result[COLUMN_TIMESTAMP] = it.getLong(timestampIndex)
                    }

                    if (deviceTypeIndex != -1) {
                        result[COLUMN_DEVICE_TYPE] = it.getString(deviceTypeIndex)
                    }

                    if (deviceNameIndex != -1) {
                        result[COLUMN_DEVICE_NAME] = it.getString(deviceNameIndex)
                    }
                    
                    return if (result.isNotEmpty()) result else null
                } else {
                    Log.e(TAG, "Cursor is empty")
                    return null
                }
            } ?: run {
                Log.e(TAG, "Failed to query ContentProvider: cursor is null")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing DMS ContentProvider", e)
            return null
        }
    }
    
    /**
     * Check if the device is registered in the DMS system
     * @return true if registered, false otherwise
     */
    fun isDeviceRegistered(): Boolean {
        // Check prerequisites
        if (!isDmsAppInstalled()) {
            Log.e(TAG, "Cannot check registration: DMS app is not installed")
            return false
        }
        
        if (!hasPermission()) {
            Log.e(TAG, "Cannot check registration: Missing permission $PERMISSION")
            return false
        }
        
        try {
            val uri = Uri.parse(PROVIDER_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val registeredIndex = it.getColumnIndex(COLUMN_REGISTERED)
                    
                    if (registeredIndex != -1) {
                        return it.getInt(registeredIndex) == 1
                    }
                }
            }
            
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking device registration", e)
            return false
        }
    }
}
