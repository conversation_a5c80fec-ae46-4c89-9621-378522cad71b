/**
 * Open Access ContentProvider Example
 * 
 * This example shows how ANY app can access the DMS ContentProvider
 * regardless of signing certificate or permissions.
 * 
 * The DMS ContentProvider is configured with:
 * - android:exported="true" 
 * - android:grantUriPermissions="true"
 * - NO readPermission or writePermission
 * 
 * This follows Android ContentProvider best practices for open data sharing.
 */

package com.example.anyclient

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.util.Log

class OpenAccessDMSClient(private val context: Context) {
    
    companion object {
        private const val TAG = "OpenAccessDMSClient"
        private const val DMS_PACKAGE = "com.nrb.dms"
        private const val DMS_AUTHORITY = "com.nrb.dms.provider"
        private const val DMS_URI = "content://$DMS_AUTHORITY/device_id"
    }
    
    /**
     * Check if DMS app is installed
     */
    fun isDMSInstalled(): <PERSON><PERSON>an {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(DMS_PACKAGE, 0)
            Log.d(TAG, "DMS app found: version ${packageInfo.versionName}")
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "DMS app not installed")
            false
        }
    }
    
    /**
     * Access DMS ContentProvider - Works for ANY app signature
     */
    fun getDeviceIdFromDMS(): DMSDeviceInfo? {
        if (!isDMSInstalled()) {
            Log.e(TAG, "Cannot access DMS: App not installed")
            return null
        }
        
        return try {
            Log.d(TAG, "Querying DMS ContentProvider at: $DMS_URI")
            
            val uri = Uri.parse(DMS_URI)
            val cursor: Cursor? = context.contentResolver.query(
                uri,
                null,  // projection - get all columns
                null,  // selection
                null,  // selectionArgs
                null   // sortOrder
            )
            
            cursor?.use { c ->
                if (c.moveToFirst()) {
                    Log.d(TAG, "Successfully got data from DMS ContentProvider")
                    
                    // Extract all available information
                    val deviceId = getColumnValue(c, "device_id")
                    val isRegistered = getColumnValue(c, "registered")?.toIntOrNull() == 1
                    val timestamp = getColumnValue(c, "timestamp")?.toLongOrNull() ?: 0L
                    val deviceType = getColumnValue(c, "device_type")
                    val deviceName = getColumnValue(c, "device_name")
                    
                    Log.d(TAG, "Device ID: $deviceId")
                    Log.d(TAG, "Registered: $isRegistered")
                    Log.d(TAG, "Device Type: $deviceType")
                    
                    return DMSDeviceInfo(
                        deviceId = deviceId,
                        isRegistered = isRegistered,
                        registrationTimestamp = timestamp,
                        deviceType = deviceType,
                        deviceName = deviceName
                    )
                } else {
                    Log.w(TAG, "No data returned from DMS ContentProvider")
                    return null
                }
            } ?: run {
                Log.e(TAG, "Failed to query DMS ContentProvider - cursor is null")
                return null
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception accessing DMS ContentProvider", e)
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing DMS ContentProvider", e)
            null
        }
    }
    
    /**
     * Test ContentProvider accessibility
     */
    fun testDMSAccess(): AccessTestResult {
        val result = AccessTestResult()
        
        // Test 1: Check if DMS app is installed
        result.isDMSInstalled = isDMSInstalled()
        if (!result.isDMSInstalled) {
            result.errorMessage = "DMS app is not installed"
            return result
        }
        
        // Test 2: Check if ContentProvider is accessible
        try {
            val uri = Uri.parse(DMS_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            
            if (cursor != null) {
                result.isContentProviderAccessible = true
                result.hasData = cursor.count > 0
                
                if (result.hasData && cursor.moveToFirst()) {
                    result.deviceId = getColumnValue(cursor, "device_id")
                    result.isRegistered = getColumnValue(cursor, "registered")?.toIntOrNull() == 1
                }
                
                cursor.close()
                result.isSuccess = true
                result.message = "Successfully accessed DMS ContentProvider"
            } else {
                result.errorMessage = "ContentProvider returned null cursor"
            }
        } catch (e: SecurityException) {
            result.errorMessage = "Security exception: ${e.message}"
        } catch (e: Exception) {
            result.errorMessage = "Error: ${e.message}"
        }
        
        return result
    }
    
    private fun getColumnValue(cursor: Cursor, columnName: String): String? {
        val index = cursor.getColumnIndex(columnName)
        return if (index != -1) cursor.getString(index) else null
    }
}

/**
 * Data classes for results
 */
data class DMSDeviceInfo(
    val deviceId: String?,
    val isRegistered: Boolean,
    val registrationTimestamp: Long,
    val deviceType: String?,
    val deviceName: String?
) {
    override fun toString(): String {
        val registrationDate = if (registrationTimestamp > 0) {
            java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                .format(java.util.Date(registrationTimestamp))
        } else "Not registered"
        
        return """
            DMS Device Information:
            - Device ID (Android ID): $deviceId
            - Registration Status: ${if (isRegistered) "Registered" else "Not Registered"}
            - Registration Date: $registrationDate
            - Device Type: $deviceType
            - Device Name: $deviceName
        """.trimIndent()
    }
}

data class AccessTestResult(
    var isDMSInstalled: Boolean = false,
    var isContentProviderAccessible: Boolean = false,
    var hasData: Boolean = false,
    var deviceId: String? = null,
    var isRegistered: Boolean = false,
    var isSuccess: Boolean = false,
    var message: String = "",
    var errorMessage: String = ""
) {
    override fun toString(): String {
        return if (isSuccess) {
            """
            ✅ DMS Access Test Results:
            - DMS App Installed: ✅
            - ContentProvider Accessible: ✅
            - Has Data: ${if (hasData) "✅" else "❌"}
            - Device ID: $deviceId
            - Registered: ${if (isRegistered) "✅" else "❌"}
            - Message: $message
            """.trimIndent()
        } else {
            """
            ❌ DMS Access Test Results:
            - DMS App Installed: ${if (isDMSInstalled) "✅" else "❌"}
            - ContentProvider Accessible: ${if (isContentProviderAccessible) "✅" else "❌"}
            - Error: $errorMessage
            """.trimIndent()
        }
    }
}

/**
 * Example usage in any Android app
 */
class ExampleActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val dmsClient = OpenAccessDMSClient(this)
        
        // Test DMS access
        val testResult = dmsClient.testDMSAccess()
        Log.d("ExampleApp", testResult.toString())
        
        if (testResult.isSuccess) {
            // Get device information
            val deviceInfo = dmsClient.getDeviceIdFromDMS()
            if (deviceInfo != null) {
                Log.d("ExampleApp", deviceInfo.toString())
                
                // Use the device ID in your app
                handleDeviceId(deviceInfo.deviceId, deviceInfo.isRegistered)
            }
        } else {
            Log.e("ExampleApp", "Failed to access DMS: ${testResult.errorMessage}")
            handleDMSNotAvailable()
        }
    }
    
    private fun handleDeviceId(deviceId: String?, isRegistered: Boolean) {
        Log.d("ExampleApp", "Got Device ID: $deviceId")
        
        // Your app logic here
        if (deviceId != null) {
            // Store device ID
            // Send to your server
            // Use for device identification
            
            if (isRegistered) {
                Log.d("ExampleApp", "Device is registered with DMS server")
            } else {
                Log.d("ExampleApp", "Device has ID but not registered with DMS server")
            }
        }
    }
    
    private fun handleDMSNotAvailable() {
        // Fallback: Get Android ID directly
        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        Log.d("ExampleApp", "Fallback to direct Android ID: $androidId")
    }
}

/**
 * Key Points:
 * 
 * 1. ✅ NO PERMISSIONS REQUIRED in AndroidManifest.xml
 * 2. ✅ Works with ANY app signature (debug, release, different certificates)
 * 3. ✅ Open access ContentProvider following Android best practices
 * 4. ✅ Graceful error handling for missing DMS app
 * 5. ✅ Comprehensive testing functionality
 * 
 * The DMS ContentProvider is configured as:
 * - android:exported="true" (accessible to other apps)
 * - android:grantUriPermissions="true" (can grant temporary permissions)
 * - No readPermission or writePermission (open access)
 * 
 * This follows the Android documentation for ContentProviders that share
 * non-sensitive data across app boundaries.
 */
