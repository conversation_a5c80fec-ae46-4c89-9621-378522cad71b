<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DMS"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">
        <activity
            android:name=".StatusActivity"
            android:exported="true"
            android:theme="@style/Theme.DMS.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Intent filter for DMS service discovery -->
            <intent-filter>
                <action android:name="com.nrb.dms.action.DEVICE_MANAGEMENT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.DMS.NoActionBar">
        </activity>

        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="@string/settings_title"
            android:theme="@style/Theme.DMS"
            android:parentActivityName=".MainActivity">
        </activity>

        <activity
            android:name=".ContentProviderTestActivity"
            android:exported="false"
            android:label="ContentProvider Test"
            android:theme="@style/Theme.DMS"
            android:parentActivityName=".MainActivity">
        </activity>
        <!-- Content Provider for sharing device ID with other apps - Open access to any app regardless of signature -->
        <provider
            android:name=".provider.DeviceRegistrationProvider"
            android:authorities="com.nrb.dms.provider"
            android:enabled="true"
            android:exported="true"
            android:grantUriPermissions="true"
            android:multiprocess="false"
            android:syncable="false">

            <!-- Metadata for ContentProvider discovery -->
            <meta-data
                android:name="com.nrb.dms.provider.description"
                android:value="DMS Device Registration Provider" />
            <meta-data
                android:name="com.nrb.dms.provider.version"
                android:value="1.0" />
        </provider>
    </application>



</manifest>