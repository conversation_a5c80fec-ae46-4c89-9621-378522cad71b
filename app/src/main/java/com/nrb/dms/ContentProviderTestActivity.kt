package com.nrb.dms

import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.nrb.dms.provider.DeviceRegistrationProvider

/**
 * Test activity to verify that the ContentProvider works correctly
 */
class ContentProviderTestActivity : AppCompatActivity() {

    private val TAG = "ContentProviderTest"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_content_provider_test)

        val resultTextView = findViewById<TextView>(R.id.resultTextView)
        val testButton = findViewById<Button>(R.id.testButton)

        testButton.setOnClickListener {
            val result = testContentProvider()
            resultTextView.text = result
        }
    }

    private fun testContentProvider(): String {
        val result = StringBuilder()
        result.append("ContentProvider Test Results\n")
        result.append("==============================\n\n")

        try {
            // Check permission
            val permission = "com.nrb.dms.permission.READ_DEVICE_ID"
            val hasPermission = checkCallingOrSelfPermission(permission) == PackageManager.PERMISSION_GRANTED
            result.append("Permission Status: ${if (hasPermission) "✓ GRANTED" else "✗ DENIED"}\n\n")

            // Test ContentProvider
            val contentUri = DeviceRegistrationProvider.CONTENT_URI
            result.append("Testing URI: $contentUri\n\n")

            val cursor = testWithUri(contentUri)
            result.append(formatCursorResult(cursor))

        } catch (e: Exception) {
            Log.e(TAG, "Error testing ContentProvider", e)
            result.append("❌ ERROR: ${e.message}\n")
        }

        return result.toString()
    }

    private fun testWithUri(uri: Uri): Cursor? {
        return contentResolver.query(
            uri,
            null,
            null,
            null,
            null
        )
    }

    private fun formatCursorResult(cursor: Cursor?): String {
        val result = StringBuilder()

        if (cursor == null) {
            return "❌ No data available"
        }

        try {
            if (cursor.moveToFirst()) {
                // Get the key information
                val deviceIdIndex = cursor.getColumnIndex("device_id")
                val registeredIndex = cursor.getColumnIndex("registered")
                val timestampIndex = cursor.getColumnIndex("timestamp")
                val deviceTypeIndex = cursor.getColumnIndex("device_type")

                val deviceId = if (deviceIdIndex != -1) cursor.getString(deviceIdIndex) else "N/A"
                val isRegistered = if (registeredIndex != -1) cursor.getInt(registeredIndex) == 1 else false
                val timestamp = if (timestampIndex != -1) cursor.getLong(timestampIndex) else 0L
                val deviceType = if (deviceTypeIndex != -1) cursor.getString(deviceTypeIndex) else "N/A"

                result.append("✓ Device Information:\n")
                result.append("  Device ID: $deviceId\n")
                result.append("  Status: ${if (isRegistered) "Registered" else "Not Registered"}\n")
                result.append("  Type: $deviceType\n")
                if (timestamp > 0) {
                    val date = java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date(timestamp))
                    result.append("  Registered: $date\n")
                }
            } else {
                result.append("❌ No data found")
            }
        } finally {
            cursor.close()
        }

        return result.toString()
    }
}
