package com.nrb.dms

import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.nrb.dms.database.DataContract

/**
 * Main activity for testing the Data Provider functionality
 */
class DataProviderTestActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "DataProviderTestActivity"
    }
    
    private lateinit var keyEditText: EditText
    private lateinit var valueEditText: EditText
    private lateinit var resultTextView: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_data_provider_test)
        
        initViews()
        setupClickListeners()
        
        // Load initial data
        loadAllData()
    }
    
    private fun initViews() {
        keyEditText = findViewById(R.id.editTextKey)
        valueEditText = findViewById(R.id.editTextValue)
        resultTextView = findViewById(R.id.textViewResult)
        
        findViewById<Button>(R.id.buttonInsert)
        findViewById<Button>(R.id.buttonQuery)
        findViewById<Button>(R.id.buttonQueryAll)
        findViewById<Button>(R.id.buttonUpdate)
        findViewById<Button>(R.id.buttonDelete)
    }
    
    private fun setupClickListeners() {
        findViewById<Button>(R.id.buttonInsert).setOnClickListener {
            insertData()
        }
        
        findViewById<Button>(R.id.buttonQuery).setOnClickListener {
            queryData()
        }
        
        findViewById<Button>(R.id.buttonQueryAll).setOnClickListener {
            loadAllData()
        }
        
        findViewById<Button>(R.id.buttonUpdate).setOnClickListener {
            updateData()
        }
        
        findViewById<Button>(R.id.buttonDelete).setOnClickListener {
            deleteData()
        }
    }
    
    private fun insertData() {
        val key = keyEditText.text.toString().trim()
        val value = valueEditText.text.toString()
        
        if (key.isEmpty()) {
            Toast.makeText(this, "Please enter a key", Toast.LENGTH_SHORT).show()
            return
        }
        
        val values = ContentValues().apply {
            put(DataContract.DataEntry.COLUMN_KEY, key)
            put(DataContract.DataEntry.COLUMN_VALUE, value)
        }
        
        try {
            val uri = contentResolver.insert(Uri.parse(DataContract.DataEntry.CONTENT_URI), values)
            if (uri != null) {
                Toast.makeText(this, "Data inserted successfully", Toast.LENGTH_SHORT).show()
                loadAllData()
                clearInputs()
            } else {
                Toast.makeText(this, "Failed to insert data", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting data", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun queryData() {
        val key = keyEditText.text.toString().trim()
        
        if (key.isEmpty()) {
            Toast.makeText(this, "Please enter a key to query", Toast.LENGTH_SHORT).show()
            return
        }
        
        try {
            val uri = Uri.parse("${DataContract.DataEntry.CONTENT_URI}/$key")
            val cursor = contentResolver.query(uri, null, null, null, null)
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val result = formatCursorData(it)
                    resultTextView.text = "Query Result:\n$result"
                } else {
                    resultTextView.text = "No data found for key: $key"
                }
            } ?: run {
                resultTextView.text = "Query failed"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error querying data", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun loadAllData() {
        try {
            val cursor = contentResolver.query(
                Uri.parse(DataContract.DataEntry.CONTENT_URI),
                null, null, null, null
            )
            
            cursor?.use {
                val result = StringBuilder("All Data:\n\n")
                
                if (it.moveToFirst()) {
                    do {
                        result.append(formatCursorData(it))
                        result.append("\n---\n")
                    } while (it.moveToNext())
                } else {
                    result.append("No data available")
                }
                
                resultTextView.text = result.toString()
            } ?: run {
                resultTextView.text = "Failed to load data"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading all data", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateData() {
        val key = keyEditText.text.toString().trim()
        val value = valueEditText.text.toString()
        
        if (key.isEmpty()) {
            Toast.makeText(this, "Please enter a key to update", Toast.LENGTH_SHORT).show()
            return
        }
        
        val values = ContentValues().apply {
            put(DataContract.DataEntry.COLUMN_VALUE, value)
        }
        
        try {
            val uri = Uri.parse("${DataContract.DataEntry.CONTENT_URI}/$key")
            val updatedRows = contentResolver.update(uri, values, null, null)
            
            if (updatedRows > 0) {
                Toast.makeText(this, "Data updated successfully", Toast.LENGTH_SHORT).show()
                loadAllData()
                clearInputs()
            } else {
                Toast.makeText(this, "No data found to update", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating data", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun deleteData() {
        val key = keyEditText.text.toString().trim()
        
        if (key.isEmpty()) {
            Toast.makeText(this, "Please enter a key to delete", Toast.LENGTH_SHORT).show()
            return
        }
        
        try {
            val uri = Uri.parse("${DataContract.DataEntry.CONTENT_URI}/$key")
            val deletedRows = contentResolver.delete(uri, null, null)
            
            if (deletedRows > 0) {
                Toast.makeText(this, "Data deleted successfully", Toast.LENGTH_SHORT).show()
                loadAllData()
                clearInputs()
            } else {
                Toast.makeText(this, "No data found to delete", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting data", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun formatCursorData(cursor: Cursor): String {
        val keyIndex = cursor.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
        val valueIndex = cursor.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)
        val timestampIndex = cursor.getColumnIndex(DataContract.DataEntry.COLUMN_TIMESTAMP)
        
        val key = if (keyIndex >= 0) cursor.getString(keyIndex) else "N/A"
        val value = if (valueIndex >= 0) cursor.getString(valueIndex) else "N/A"
        val timestamp = if (timestampIndex >= 0) cursor.getLong(timestampIndex) else 0L
        
        return "Key: $key\nValue: $value\nTimestamp: $timestamp"
    }
    
    private fun clearInputs() {
        keyEditText.text.clear()
        valueEditText.text.clear()
    }
}
