package com.nrb.dms.api

import com.nrb.dms.model.DeviceInfo
import com.nrb.dms.model.DeviceRegistrationResponse
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * Retrofit interface for device registration API
 */
interface DeviceRegistrationApi {

    @POST("Devices")
    fun registerDevice(@Body deviceInfo: DeviceInfo): Call<DeviceRegistrationResponse>

    @GET("Devices/{id}")
    fun getDeviceById(@Path("id") deviceId: String): Call<DeviceRegistrationResponse>
}
