package com.nrb.dms.api

import android.content.Context
import com.nrb.dms.utils.PreferencesManager
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Singleton class for Retrofit client
 */
object RetrofitClient {
    private lateinit var preferencesManager: PreferencesManager
    private var isInitialized = false

    /**
     * Initialize the RetrofitClient with a context
     * Must be called before using the client
     */
    fun initialize(context: Context) {
        if (!isInitialized) {
            preferencesManager = PreferencesManager.getInstance(context)
            isInitialized = true
        }
    }

    /**
     * Get the base URL from preferences
     */
    private fun getBaseUrl(): String {
        if (!isInitialized) {
            throw IllegalStateException("RetrofitClient must be initialized before use")
        }
        return preferencesManager.getServerUrl()
    }

    // Create OkHttpClient with logging and timeout settings
    private val okHttpClient: OkHttpClient by lazy {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        // Create a trust manager that does not validate certificate chains for development
        val trustAllCerts = arrayOf<javax.net.ssl.TrustManager>(object : javax.net.ssl.X509TrustManager {
            override fun checkClientTrusted(chain: Array<out java.security.cert.X509Certificate>?, authType: String?) {}
            override fun checkServerTrusted(chain: Array<out java.security.cert.X509Certificate>?, authType: String?) {}
            override fun getAcceptedIssuers(): Array<java.security.cert.X509Certificate> = arrayOf()
        })

        // Install the all-trusting trust manager
        val sslContext = javax.net.ssl.SSLContext.getInstance("SSL")
        sslContext.init(null, trustAllCerts, java.security.SecureRandom())

        // Create an ssl socket factory with our all-trusting manager
        val sslSocketFactory = sslContext.socketFactory

        // Get the base URL to determine if we need SSL configuration
        val baseUrl = getBaseUrl()

        val builder = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)

        // Only add SSL configuration for HTTPS URLs
        if (baseUrl.startsWith("https", ignoreCase = true)) {
            builder.sslSocketFactory(sslSocketFactory, trustAllCerts[0] as javax.net.ssl.X509TrustManager)
                .hostnameVerifier { _, _ -> true } // Disable hostname verification
        }

        builder.build()
    }

    // Create Retrofit instance
    private val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(getBaseUrl())
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    // Create API service
    private var _deviceRegistrationApi: DeviceRegistrationApi? = null
    val deviceRegistrationApi: DeviceRegistrationApi
        get() {
            if (_deviceRegistrationApi == null) {
                _deviceRegistrationApi = retrofit.create(DeviceRegistrationApi::class.java)
            }
            return _deviceRegistrationApi!!
        }

    /**
     * Recreate the Retrofit instance and API service when the URL changes
     */
    fun resetClient() {
        _deviceRegistrationApi = null
    }
}
