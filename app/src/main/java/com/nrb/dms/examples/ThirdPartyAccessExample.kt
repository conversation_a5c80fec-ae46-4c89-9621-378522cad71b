package com.nrb.dms.examples

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.util.Log
// Note: DeviceRegistrationHelper has been replaced with generic DataContentProvider

/**
 * Example class showing how a third-party app would access the device ID
 * This is for documentation purposes only and would be implemented in the third-party app
 */
class ThirdPartyAccessExample(private val context: Context) {

    companion object {
        private const val TAG = "ThirdPartyAccess"
        private const val PERMISSION = "com.nrb.dms.permission.READ_DEVICE_ID"
        private const val DMS_PACKAGE = "com.nrb.dms"
        private const val PROVIDER_URI = "content://com.nrb.dms.provider/device_id"
    }

    /**
     * Example method showing how to access the device ID and device type
     */
    fun accessDeviceId() {
        // First, check if the DMS app is installed
        if (!isDmsAppInstalled()) {
            Log.e(TAG, "DMS app is not installed")
            return
        }

        // Check if we have the required permission
        if (!hasPermission()) {
            Log.e(TAG, "Missing permission: $PERMISSION")
            return
        }

        // Method 1: Direct ContentProvider access
        accessViaContentProvider()

        // Method 2: Using the helper class
        accessViaHelper()
    }

    /**
     * Check if the DMS app is installed
     */
    private fun isDmsAppInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo(DMS_PACKAGE, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * Check if the app has permission to access device ID
     */
    private fun hasPermission(): Boolean {
        return context.checkCallingOrSelfPermission(PERMISSION) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Access device information directly via ContentProvider
     */
    private fun accessViaContentProvider() {
        try {
            Log.d(TAG, "Attempting to access ContentProvider at URI: $PROVIDER_URI")
            val uri = Uri.parse(PROVIDER_URI)
            val cursor: Cursor? = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                if (it.moveToFirst()) {
                    // Get column indices
                    val deviceIdIndex = it.getColumnIndex("device_id")
                    val registeredIndex = it.getColumnIndex("registered")
                    val timestampIndex = it.getColumnIndex("timestamp")
                    val deviceTypeIndex = it.getColumnIndex("device_type")
                    val deviceNameIndex = it.getColumnIndex("device_name")

                    // Log column indices for debugging
                    Log.d(TAG, "Column indices: deviceId=$deviceIdIndex, registered=$registeredIndex, " +
                            "timestamp=$timestampIndex, deviceType=$deviceTypeIndex, deviceName=$deviceNameIndex")

                    // Check if required columns exist
                    if (deviceIdIndex != -1 && registeredIndex != -1) {
                        val isRegistered = it.getInt(registeredIndex) == 1

                        if (isRegistered) {
                            val deviceId = it.getString(deviceIdIndex)
                            Log.d(TAG, "Device ID: $deviceId")

                            // Get additional information if available
                            if (timestampIndex != -1) {
                                val timestamp = it.getLong(timestampIndex)
                                Log.d(TAG, "Registration timestamp: $timestamp")
                            }

                            if (deviceTypeIndex != -1) {
                                val deviceType = it.getString(deviceTypeIndex)
                                Log.d(TAG, "Device type: $deviceType")
                            }

                            if (deviceNameIndex != -1) {
                                val deviceName = it.getString(deviceNameIndex)
                                Log.d(TAG, "Device name: $deviceName")
                            } else {

                            }
                        } else {
                            Log.d(TAG, "Device is not registered yet")
                        }
                    } else {
                        Log.e(TAG, "Required columns not found in cursor")
                    }
                } else {
                    Log.e(TAG, "Cursor is empty")
                }
            } ?: Log.e(TAG, "Cursor is null")
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing ContentProvider", e)
        }
    }

    /**
     * Access device information via new DataContentProvider
     * Note: The old DeviceRegistrationHelper has been replaced with a generic key-value provider
     */
    private fun accessViaNewProvider() {
        try {
            // The new provider uses a generic key-value structure
            // You would need to query specific keys for device information
            val uri = Uri.parse("content://com.nrb.dms.provider/data/device_id")
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex("key")
                    val valueIndex = it.getColumnIndex("value")
                    val timestampIndex = it.getColumnIndex("timestamp")

                    if (keyIndex >= 0 && valueIndex >= 0) {
                        val key = it.getString(keyIndex)
                        val value = it.getString(valueIndex)
                        val timestamp = if (timestampIndex >= 0) it.getLong(timestampIndex) else 0L

                        Log.d(TAG, "Found data - Key: $key, Value: $value, Timestamp: $timestamp")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error using new DataContentProvider", e)
        }
    }

    /**
     * Example of how to declare the permission in the third-party app's manifest
     *
     * <uses-permission android:name="com.nrb.dms.permission.READ_DEVICE_ID" />
     */
}
