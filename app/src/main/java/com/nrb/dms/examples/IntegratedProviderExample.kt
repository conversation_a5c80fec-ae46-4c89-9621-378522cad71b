package com.nrb.dms.examples

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.util.Log
import com.nrb.dms.database.DataContract
import com.nrb.dms.manager.DeviceDataManager
import com.nrb.dms.provider.DeviceRegistrationHelper

/**
 * Comprehensive example showing how to use the integrated DMS system:
 * 1. Generic DataContentProvider for any key-value data
 * 2. DeviceRegistrationHelper for device-specific data
 * 3. Direct ContentProvider access for custom queries
 */
class IntegratedProviderExample(private val context: Context) {

    companion object {
        private const val TAG = "IntegratedProviderExample"
        private const val DMS_PACKAGE = "com.nrb.dms"
    }

    /**
     * Demonstrate all access methods
     */
    fun demonstrateAllAccessMethods() {
        Log.d(TAG, "=== DMS Integrated Provider Example ===")

        // Check if DMS app is installed
        if (!isDmsAppInstalled()) {
            Log.e(TAG, "DMS app is not installed")
            return
        }

        // Method 1: Use DeviceRegistrationHelper for device-specific data
        accessDeviceDataViaHelper()

        // Method 2: Use generic DataContentProvider for any key-value data
        accessGenericDataViaProvider()

        // Method 3: Direct ContentProvider access for custom queries
        accessDataDirectly()

        // Method 4: Query all data and filter device-related entries
        queryAllDataAndFilter()
    }

    /**
     * Method 1: Use DeviceRegistrationHelper for device-specific data
     * This is the recommended approach for third-party apps accessing device information
     */
    private fun accessDeviceDataViaHelper() {
        Log.d(TAG, "--- Method 1: DeviceRegistrationHelper ---")

        try {
            val helper = DeviceRegistrationHelper(context)

            // Check if device is registered
            val isRegistered = helper.isDeviceRegistered()
            Log.d(TAG, "Device registered: " + isRegistered)

            if (isRegistered) {
                // Get core device information
                val deviceId = helper.getDeviceId()
                val timestamp = helper.getRegistrationTimestamp()
                val deviceType = helper.getDeviceType()
                val deviceName = helper.getDeviceName()

                Log.d(TAG, "Device ID: " + deviceId)
                Log.d(TAG, "Registration timestamp: " + timestamp)
                Log.d(TAG, "Device type: " + deviceType)
                Log.d(TAG, "Device name: " + deviceName)

                // Get additional device information
                val deviceModel = helper.getDeviceModel()
                val manufacturer = helper.getManufacturer()
                val androidVersion = helper.getAndroidVersion()
                val apiLevel = helper.getApiLevel()

                Log.d(TAG, "Device model: " + deviceModel)
                Log.d(TAG, "Manufacturer: " + manufacturer)
                Log.d(TAG, "Android version: " + androidVersion)
                Log.d(TAG, "API level: " + apiLevel)

                // Get all device data as a map
                val allDeviceData = helper.getAllDeviceData()
                Log.d(TAG, "All device data: " + allDeviceData)
            } else {
                Log.d(TAG, "Device is not registered yet")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error using DeviceRegistrationHelper", e)
        }
    }

    /**
     * Method 2: Use generic DataContentProvider for any key-value data
     * This shows how to store and retrieve custom data alongside device information
     */
    private fun accessGenericDataViaProvider() {
        Log.d(TAG, "--- Method 2: Generic DataContentProvider ---")

        try {
            // Query a specific device-related key
            val deviceIdUri = Uri.parse(DataContract.DataEntry.CONTENT_URI + "/" + DeviceDataManager.KEY_DEVICE_ID)
            val cursor = context.contentResolver.query(deviceIdUri, null, null, null, null)

            cursor?.use {
                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)
                    val timestampIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_TIMESTAMP)

                    if (keyIndex >= 0 && valueIndex >= 0 && timestampIndex >= 0) {
                        val key = it.getString(keyIndex)
                        val value = it.getString(valueIndex)
                        val timestamp = it.getLong(timestampIndex)

                        Log.d(TAG, "Found key-value pair: " + key + " = " + value + " (timestamp: " + timestamp + ")")
                    } else {
                        Log.d(TAG, "Invalid column indices in cursor")
                    }
                } else {
                    Log.d(TAG, "No data found for device_id key")
                }
            }

            // You can also query custom keys if your app stores additional data
            // For example, if you stored app-specific settings:
            // val customUri = Uri.parse(DataContract.DataEntry.CONTENT_URI + "/my_app_setting")
            // val customCursor = context.contentResolver.query(customUri, null, null, null, null)

        } catch (e: Exception) {
            Log.e(TAG, "Error using generic DataContentProvider", e)
        }
    }

    /**
     * Method 3: Direct ContentProvider access for custom queries
     * This shows how to perform more complex queries
     */
    private fun accessDataDirectly() {
        Log.d(TAG, "--- Method 3: Direct ContentProvider Access ---")

        try {
            // Query all data from the provider
            val uri = Uri.parse(DataContract.DataEntry.CONTENT_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                Log.d(TAG, "Total entries in provider: " + it.count)

                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)
                    val timestampIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_TIMESTAMP)

                    if (keyIndex >= 0 && valueIndex >= 0 && timestampIndex >= 0) {
                        do {
                            val key = it.getString(keyIndex)
                            val value = it.getString(valueIndex)
                            val timestamp = it.getLong(timestampIndex)

                            Log.d(TAG, "Entry: " + key + " = " + value + " (timestamp: " + timestamp + ")")
                        } while (it.moveToNext())
                    } else {
                        Log.d(TAG, "Invalid column indices in cursor")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error with direct ContentProvider access", e)
        }
    }

    /**
     * Method 4: Query all data and filter device-related entries
     * This shows how to separate device data from other app data
     */
    private fun queryAllDataAndFilter() {
        Log.d(TAG, "--- Method 4: Query All Data and Filter ---")

        try {
            val uri = Uri.parse(DataContract.DataEntry.CONTENT_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                val deviceData = mutableMapOf<String, String>()
                val otherData = mutableMapOf<String, String>()

                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)

                    if (keyIndex >= 0 && valueIndex >= 0) {
                        do {
                            val key = it.getString(keyIndex)
                            val value = it.getString(valueIndex)

                            if (isDeviceRelatedKey(key)) {
                                deviceData[key] = value
                            } else {
                                otherData[key] = value
                            }
                        } while (it.moveToNext())
                    }
                }

                Log.d(TAG, "Device-related data: " + deviceData)
                Log.d(TAG, "Other data: " + otherData)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error querying and filtering data", e)
        }
    }

    /**
     * Check if the DMS app is installed
     */
    private fun isDmsAppInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo(DMS_PACKAGE, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * Check if a key is device-related
     */
    private fun isDeviceRelatedKey(key: String): Boolean {
        return key.startsWith("device_") ||
               key == DeviceDataManager.KEY_REGISTERED ||
               key == DeviceDataManager.KEY_REGISTRATION_TIMESTAMP ||
               key == DeviceDataManager.KEY_MANUFACTURER ||
               key == DeviceDataManager.KEY_ANDROID_VERSION ||
               key == DeviceDataManager.KEY_API_LEVEL ||
               key == DeviceDataManager.KEY_BRAND ||
               key == DeviceDataManager.KEY_PRODUCT ||
               key == DeviceDataManager.KEY_HARDWARE ||
               key == DeviceDataManager.KEY_FINGERPRINT ||
               key == DeviceDataManager.KEY_LOCALE ||
               key == DeviceDataManager.KEY_TIMEZONE
    }
}
