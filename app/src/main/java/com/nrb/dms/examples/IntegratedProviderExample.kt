package com.nrb.dms.examples

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.util.Log
import com.nrb.dms.database.DataContract
import com.nrb.dms.manager.DeviceDataManager
import com.nrb.dms.provider.DeviceRegistrationHelper

/**
 * Professional example demonstrating the integrated DMS ContentProvider system.
 *
 * This class showcases three primary access patterns:
 * 1. **DeviceRegistrationHelper** - High-level API for device-specific data (recommended)
 * 2. **Generic DataContentProvider** - Direct key-value access for custom data
 * 3. **Direct ContentProvider queries** - Low-level access for advanced use cases
 *
 * @param context Application context for ContentProvider access
 *
 * <AUTHOR> Development Team
 * @since 2.0
 */
class IntegratedProviderExample(private val context: Context) {

    companion object {
        private const val TAG = "IntegratedProviderExample"
        private const val DMS_PACKAGE_NAME = "com.nrb.dms"

        // Error messages
        private const val ERROR_DMS_NOT_INSTALLED = "DMS app is not installed on this device"
        private const val ERROR_DEVICE_NOT_REGISTERED = "Device is not registered with DMS"
        private const val ERROR_CONTENT_PROVIDER_ACCESS = "Failed to access ContentProvider"
    }

    /**
     * Demonstrates all available access methods for the DMS ContentProvider system.
     *
     * This method serves as a comprehensive example showing different approaches
     * to accessing device registration data and custom key-value pairs.
     *
     * @throws SecurityException if ContentProvider access is denied
     * @throws IllegalStateException if DMS app is not installed
     */
    fun demonstrateAllAccessMethods() {
        Log.i(TAG, "Starting DMS Integrated Provider demonstration")

        // Validate prerequisites
        validatePrerequisites()

        try {
            // Method 1: Recommended approach for device data
            demonstrateDeviceRegistrationHelper()

            // Method 2: Generic key-value data access
            demonstrateGenericDataAccess()

            // Method 3: Advanced direct queries
            demonstrateDirectContentProviderAccess()

            // Method 4: Data filtering and categorization
            demonstrateDataFiltering()

            Log.i(TAG, "DMS Provider demonstration completed successfully")
        } catch (Exception e) {
            Log.e(TAG, "Error during demonstration", e)
            throw e
        }
    }

    /**
     * Demonstrates the DeviceRegistrationHelper - the recommended approach for accessing device data.
     *
     * This method shows how third-party applications should interact with device registration
     * information using the high-level helper API.
     */
    private fun demonstrateDeviceRegistrationHelper() {
        Log.i(TAG, "=== Method 1: DeviceRegistrationHelper (Recommended) ===")

        try {
            val helper = DeviceRegistrationHelper(context)

            // Check registration status
            val isRegistered = helper.isDeviceRegistered()
            Log.i(TAG, "Device registration status: " + if (isRegistered) "REGISTERED" else "NOT_REGISTERED")

            if (!isRegistered) {
                Log.w(TAG, ERROR_DEVICE_NOT_REGISTERED)
                return
            }

            // Retrieve core device information
            val deviceInfo = retrieveCoreDeviceInfo(helper)
            logDeviceInfo("Core Device Information", deviceInfo)

            // Retrieve extended device information
            val extendedInfo = retrieveExtendedDeviceInfo(helper)
            logDeviceInfo("Extended Device Information", extendedInfo)

            // Retrieve all device data as a comprehensive map
            val allDeviceData = helper.getAllDeviceData()
            Log.i(TAG, "Complete device data retrieved: " + allDeviceData.size + " entries")

        } catch (SecurityException e) {
            Log.e(TAG, "Security error accessing device data", e)
            throw e
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error in DeviceRegistrationHelper demonstration", e)
            throw e
        }
    }

    /**
     * Demonstrates generic DataContentProvider access for key-value data operations.
     *
     * This method shows how to query specific keys and demonstrates the flexibility
     * of the generic key-value storage system.
     */
    private fun demonstrateGenericDataAccess() {
        Log.i(TAG, "=== Method 2: Generic DataContentProvider Access ===")

        try {
            // Query a specific device-related key
            val deviceIdResult = querySpecificKey(DeviceDataManager.KEY_DEVICE_ID)
            if (deviceIdResult != null) {
                Log.i(TAG, "Device ID query successful: " + deviceIdResult.key + " = " + deviceIdResult.value)
            } else {
                Log.w(TAG, "Device ID not found in ContentProvider")
            }

            // Demonstrate querying other device keys
            val deviceTypeResult = querySpecificKey(DeviceDataManager.KEY_DEVICE_TYPE)
            if (deviceTypeResult != null) {
                Log.i(TAG, "Device type: " + deviceTypeResult.value)
            }

            // Example of how third-party apps could query custom data
            // (This would only work if custom data was previously stored)
            demonstrateCustomDataQuery()

        } catch (SecurityException e) {
            Log.e(TAG, "Security error accessing ContentProvider", e)
            throw e
        } catch (Exception e) {
            Log.e(TAG, "Error in generic DataContentProvider demonstration", e)
            throw e
        }
    }

    /**
     * Demonstrates direct ContentProvider access for advanced querying scenarios.
     *
     * This method shows how to perform bulk queries and iterate through all
     * available data in the ContentProvider.
     */
    private fun demonstrateDirectContentProviderAccess() {
        Log.i(TAG, "=== Method 3: Direct ContentProvider Access (Advanced) ===")

        try {
            val uri = Uri.parse(DataContract.DataEntry.CONTENT_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                val totalEntries = it.count
                Log.i(TAG, "ContentProvider contains " + totalEntries + " total entries")

                if (totalEntries == 0) {
                    Log.w(TAG, "No data found in ContentProvider")
                    return
                }

                val entries = mutableListOf<KeyValueResult>()

                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)
                    val timestampIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_TIMESTAMP)

                    if (keyIndex >= 0 && valueIndex >= 0 && timestampIndex >= 0) {
                        do {
                            entries.add(KeyValueResult(
                                key = it.getString(keyIndex),
                                value = it.getString(valueIndex),
                                timestamp = it.getLong(timestampIndex)
                            ))
                        } while (it.moveToNext())
                    } else {
                        throw IllegalStateException("Invalid cursor column indices")
                    }
                }

                // Log summary of retrieved data
                Log.i(TAG, "Successfully retrieved " + entries.size + " entries")
                entries.take(3).forEach { entry ->
                    Log.d(TAG, "Sample entry: " + entry.key + " = " + entry.value)
                }

            } ?: throw IllegalStateException(ERROR_CONTENT_PROVIDER_ACCESS)

        } catch (SecurityException e) {
            Log.e(TAG, "Security error in direct ContentProvider access", e)
            throw e
        } catch (Exception e) {
            Log.e(TAG, "Error in direct ContentProvider access demonstration", e)
            throw e
        }
    }

    /**
     * Demonstrates data filtering and categorization capabilities.
     *
     * This method shows how to separate device-related data from custom application
     * data, which is useful for applications that store both types of information.
     */
    private fun demonstrateDataFiltering() {
        Log.i(TAG, "=== Method 4: Data Filtering and Categorization ===")

        try {
            val uri = Uri.parse(DataContract.DataEntry.CONTENT_URI)
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                val deviceData = mutableMapOf<String, String>()
                val customData = mutableMapOf<String, String>()

                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)

                    if (keyIndex >= 0 && valueIndex >= 0) {
                        do {
                            val key = it.getString(keyIndex)
                            val value = it.getString(valueIndex)

                            if (isDeviceRelatedKey(key)) {
                                deviceData[key] = value
                            } else {
                                customData[key] = value
                            }
                        } while (it.moveToNext())
                    } else {
                        throw IllegalStateException("Invalid cursor column indices")
                    }
                }

                // Log categorized results
                Log.i(TAG, "Device-related entries: " + deviceData.size)
                Log.i(TAG, "Custom application entries: " + customData.size)

                if (deviceData.isNotEmpty()) {
                    Log.d(TAG, "Device data keys: " + deviceData.keys.joinToString(", "))
                }

                if (customData.isNotEmpty()) {
                    Log.d(TAG, "Custom data keys: " + customData.keys.joinToString(", "))
                }

            } ?: throw IllegalStateException(ERROR_CONTENT_PROVIDER_ACCESS)

        } catch (SecurityException e) {
            Log.e(TAG, "Security error in data filtering demonstration", e)
            throw e
        } catch (Exception e) {
            Log.e(TAG, "Error in data filtering demonstration", e)
            throw e
        }
    }

    // ========================================
    // VALIDATION AND UTILITY METHODS
    // ========================================

    /**
     * Validates that all prerequisites for DMS ContentProvider access are met.
     *
     * @throws IllegalStateException if DMS app is not installed
     */
    private fun validatePrerequisites() {
        if (!isDmsAppInstalled()) {
            throw IllegalStateException(ERROR_DMS_NOT_INSTALLED)
        }
        Log.d(TAG, "Prerequisites validated successfully")
    }

    /**
     * Checks if the DMS application is installed on the device.
     *
     * @return true if DMS app is installed, false otherwise
     */
    private fun isDmsAppInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo(DMS_PACKAGE_NAME, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.w(TAG, "DMS app not found: " + DMS_PACKAGE_NAME)
            false
        }
    }

    // ========================================
    // HELPER METHODS FOR DATA RETRIEVAL
    // ========================================

    /**
     * Retrieves core device information using DeviceRegistrationHelper.
     */
    private fun retrieveCoreDeviceInfo(helper: DeviceRegistrationHelper): Map<String, String?> {
        return mapOf(
            "Device ID" to helper.getDeviceId(),
            "Registration Timestamp" to helper.getRegistrationTimestamp().toString(),
            "Device Type" to helper.getDeviceType(),
            "Device Name" to helper.getDeviceName()
        )
    }

    /**
     * Retrieves extended device information using DeviceRegistrationHelper.
     */
    private fun retrieveExtendedDeviceInfo(helper: DeviceRegistrationHelper): Map<String, String?> {
        return mapOf(
            "Device Model" to helper.getDeviceModel(),
            "Manufacturer" to helper.getManufacturer(),
            "Android Version" to helper.getAndroidVersion(),
            "API Level" to helper.getApiLevel().toString()
        )
    }

    /**
     * Queries a specific key from the ContentProvider.
     *
     * @param key The key to query
     * @return KeyValueResult containing the data, or null if not found
     */
    private fun querySpecificKey(key: String): KeyValueResult? {
        try {
            val uri = Uri.parse(DataContract.DataEntry.CONTENT_URI + "/" + key)
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            return cursor?.use {
                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)
                    val timestampIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_TIMESTAMP)

                    if (keyIndex >= 0 && valueIndex >= 0 && timestampIndex >= 0) {
                        KeyValueResult(
                            key = it.getString(keyIndex),
                            value = it.getString(valueIndex),
                            timestamp = it.getLong(timestampIndex)
                        )
                    } else {
                        null
                    }
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error querying key: " + key, e)
            return null
        }
    }

    /**
     * Demonstrates how to query custom application data.
     */
    private fun demonstrateCustomDataQuery() {
        Log.d(TAG, "Demonstrating custom data query (example only)")
        // This is an example of how third-party apps could query their own stored data
        // val customResult = querySpecificKey("my_app_custom_setting")
        // if (customResult != null) {
        //     Log.i(TAG, "Custom data found: " + customResult.value)
        // }
    }

    /**
     * Logs device information in a structured format.
     */
    private fun logDeviceInfo(category: String, info: Map<String, String?>) {
        Log.i(TAG, category + ":")
        info.forEach { (key, value) ->
            Log.i(TAG, "  " + key + ": " + (value ?: "N/A"))
        }
    }

    /**
     * Determines if a key is related to device registration data.
     */
    private fun isDeviceRelatedKey(key: String): Boolean {
        return key.startsWith("device_") ||
               key == DeviceDataManager.KEY_REGISTERED ||
               key == DeviceDataManager.KEY_REGISTRATION_TIMESTAMP ||
               key == DeviceDataManager.KEY_MANUFACTURER ||
               key == DeviceDataManager.KEY_ANDROID_VERSION ||
               key == DeviceDataManager.KEY_API_LEVEL ||
               key == DeviceDataManager.KEY_BRAND ||
               key == DeviceDataManager.KEY_PRODUCT ||
               key == DeviceDataManager.KEY_HARDWARE ||
               key == DeviceDataManager.KEY_FINGERPRINT ||
               key == DeviceDataManager.KEY_LOCALE ||
               key == DeviceDataManager.KEY_TIMEZONE
    }

    // ========================================
    // DATA CLASSES
    // ========================================

    /**
     * Data class representing a key-value result from the ContentProvider.
     */
    private data class KeyValueResult(
        val key: String,
        val value: String,
        val timestamp: Long
    )
}
