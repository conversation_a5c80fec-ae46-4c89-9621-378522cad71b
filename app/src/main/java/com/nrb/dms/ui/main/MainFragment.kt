package com.nrb.dms.ui.main

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.nrb.dms.R
import com.nrb.dms.databinding.FragmentMainBinding
import com.nrb.dms.utils.PreferencesManager
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class MainFragment : Fragment() {

    private var _binding: FragmentMainBinding? = null
    private val binding get() = _binding!!
    private lateinit var preferencesManager: PreferencesManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMainBinding.inflate(inflater, container, false)
        preferencesManager = PreferencesManager.getInstance(requireContext())

        setupUI()
        loadDeviceInfo()

        return binding.root
    }

    private fun setupUI() {
        // Set up copy button
        binding.copyDeviceIdBtn.setOnClickListener {
            copyDeviceIdToClipboard()
        }

        // Set up refresh button
        binding.refreshDeviceIdBtn.setOnClickListener {
            loadDeviceInfo()
        }
    }

    private fun loadDeviceInfo() {
        val deviceId = preferencesManager.getDeviceId()

        // Display Device ID (which is Android ID)
        if (deviceId != null && deviceId.isNotEmpty()) {
            binding.deviceIdValue.text = deviceId
            binding.copyDeviceIdBtn.isEnabled = true
        } else {
            binding.deviceIdValue.text = getString(R.string.device_id_not_available)
            binding.copyDeviceIdBtn.isEnabled = false
        }

        // Display registration status
        if (preferencesManager.isDeviceRegistered()) {
            val timestamp = preferencesManager.getRegistrationTimestamp()
            val dateFormat = SimpleDateFormat("MMM dd, yyyy hh:mm a", Locale.getDefault())
            val formattedDate = dateFormat.format(Date(timestamp))
            binding.statusInfo.text = getString(R.string.registered_on, formattedDate)
        } else {
            binding.statusInfo.text = getString(R.string.device_not_registered)
        }
    }

    private fun copyDeviceIdToClipboard() {
        val deviceId = preferencesManager.getDeviceId()

        if (deviceId != null && deviceId.isNotEmpty()) {
            val clipboard = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("Device ID", deviceId)
            clipboard.setPrimaryClip(clip)

            Toast.makeText(requireContext(), getString(R.string.device_id_copied), Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), getString(R.string.device_id_not_available), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh device info when fragment becomes visible
        loadDeviceInfo()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
