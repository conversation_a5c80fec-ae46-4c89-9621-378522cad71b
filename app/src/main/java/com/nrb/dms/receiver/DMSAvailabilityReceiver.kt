package com.nrb.dms.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.nrb.dms.utils.PreferencesManager

/**
 * Broadcast receiver that announces DMS availability to other apps
 */
class DMSAvailabilityReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "DMSAvailabilityReceiver"
        const val ACTION_DMS_AVAILABLE = "com.nrb.dms.action.DMS_AVAILABLE"
        const val ACTION_DMS_DEVICE_REGISTERED = "com.nrb.dms.action.DEVICE_REGISTERED"
        const val EXTRA_DEVICE_ID = "device_id"
        const val EXTRA_PROVIDER_AUTHORITY = "provider_authority"

        /**
         * Manually trigger DMS availability announcement
         */
        fun announceDMSAvailabilityManually(context: Context) {
            val receiver = DMSAvailabilityReceiver()
            val intent = Intent(Intent.ACTION_BOOT_COMPLETED)
            receiver.onReceive(context, intent)
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received broadcast: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                // Announce DMS availability after device boot
                announceDMSAvailability(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                // Announce DMS availability after app update
                if (intent.dataString?.contains("com.nrb.dms") == true) {
                    announceDMSAvailability(context)
                }
            }
        }
    }
    
    /**
     * Send broadcast to announce DMS availability
     */
    private fun announceDMSAvailability(context: Context) {
        try {
            val preferencesManager = PreferencesManager.getInstance(context)
            val deviceId = preferencesManager.getDeviceId() // This is always the Android ID

            // Always broadcast DMS availability with device ID (Android ID)
            val intent = Intent(ACTION_DMS_AVAILABLE).apply {
                putExtra(EXTRA_PROVIDER_AUTHORITY, "com.nrb.dms.provider")
                // Always include device ID since Android ID is always available
                putExtra(EXTRA_DEVICE_ID, deviceId ?: "unknown")
                putExtra("registration_status", preferencesManager.isDeviceRegistered())
                // Make it a system-wide broadcast so other apps can receive it
                // Remove setPackage to allow other apps to receive this broadcast
            }

            context.sendBroadcast(intent)
            Log.d(TAG, "Announced DMS availability with Device ID: $deviceId, Registered: ${preferencesManager.isDeviceRegistered()}")

            // Always send device registration status broadcast
            val statusIntent = Intent(ACTION_DMS_DEVICE_REGISTERED).apply {
                putExtra(EXTRA_DEVICE_ID, deviceId ?: "unknown")
                putExtra(EXTRA_PROVIDER_AUTHORITY, "com.nrb.dms.provider")
                putExtra("is_registered", preferencesManager.isDeviceRegistered())
                putExtra("registration_timestamp", preferencesManager.getRegistrationTimestamp())
            }

            context.sendBroadcast(statusIntent)
            Log.d(TAG, "Announced device status - ID: $deviceId, Registered: ${preferencesManager.isDeviceRegistered()}")

        } catch (e: Exception) {
            Log.e(TAG, "Error announcing DMS availability", e)
        }
    }
}
