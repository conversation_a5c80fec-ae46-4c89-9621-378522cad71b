package com.nrb.dms.database

import android.provider.BaseColumns

/**
 * Contract class for the data provider database
 */
object DataContract {
    
    // Content provider authority
    const val AUTHORITY = "com.nrb.dms.provider"
    
    // Base content URI
    const val BASE_CONTENT_URI = "content://$AUTHORITY"
    
    // Path for data table
    const val PATH_DATA = "data"
    
    /**
     * Inner class that defines the table contents for the data table
     */
    object DataEntry : BaseColumns {
        // Table name
        const val TABLE_NAME = "data"
        
        // Column names
        const val COLUMN_KEY = "key"
        const val COLUMN_VALUE = "value"
        const val COLUMN_TIMESTAMP = "timestamp"
        
        // Content URI for the data table
        const val CONTENT_URI = "$BASE_CONTENT_URI/$PATH_DATA"
        
        // MIME types
        const val CONTENT_TYPE = "vnd.android.cursor.dir/vnd.dataprovider.data"
        const val CONTENT_ITEM_TYPE = "vnd.android.cursor.item/vnd.dataprovider.data"
    }
    
    // URI matcher codes
    const val DATA = 100
    const val DATA_WITH_KEY = 101
}
