package com.nrb.dms.database

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.util.Log

/**
 * Database helper for managing key-value data storage
 */
class DataDbHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {
    
    companion object {
        private const val TAG = "DataDbHelper"
        private const val DATABASE_NAME = "data_provider.db"
        private const val DATABASE_VERSION = 1
        
        // SQL statement to create the data table
        private val SQL_CREATE_TABLE =
            "CREATE TABLE ${DataContract.DataEntry.TABLE_NAME} (" +
                "${DataContract.DataEntry._ID} INTEGER PRIMARY KEY AUTOINCREMENT," +
                "${DataContract.DataEntry.COLUMN_KEY} TEXT UNIQUE NOT NULL," +
                "${DataContract.DataEntry.COLUMN_VALUE} TEXT NOT NULL," +
                "${DataContract.DataEntry.COLUMN_TIMESTAMP} INTEGER NOT NULL" +
            ")"

        // SQL statement to drop the data table
        private val SQL_DELETE_TABLE = "DROP TABLE IF EXISTS ${DataContract.DataEntry.TABLE_NAME}"
    }
    
    override fun onCreate(db: SQLiteDatabase) {
        Log.d(TAG, "Creating database table")
        db.execSQL(SQL_CREATE_TABLE)
    }
    
    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        Log.d(TAG, "Upgrading database from version $oldVersion to $newVersion")
        db.execSQL(SQL_DELETE_TABLE)
        onCreate(db)
    }
    
    /**
     * Insert or update data with the given key and value
     * @param key The key to insert/update
     * @param value The value to store
     * @return The row ID of the inserted/updated row, or -1 if error
     */
    fun insertOrUpdateData(key: String, value: String): Long {
        val timestamp = System.currentTimeMillis()

        val values = ContentValues().apply {
            put(DataContract.DataEntry.COLUMN_KEY, key)
            put(DataContract.DataEntry.COLUMN_VALUE, value)
            put(DataContract.DataEntry.COLUMN_TIMESTAMP, timestamp)
        }

        return try {
            val db = writableDatabase

            // Use INSERT OR REPLACE for simpler upsert operation
            val result = db.insertWithOnConflict(
                DataContract.DataEntry.TABLE_NAME,
                null,
                values,
                SQLiteDatabase.CONFLICT_REPLACE
            )

            if (result != -1L) {
                Log.d(TAG, "Successfully inserted/updated key: $key")
                result
            } else {
                Log.w(TAG, "Failed to insert/update key: $key")
                -1L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting/updating data for key: $key", e)
            -1L
        }
    }
    
    /**
     * Get data by key
     * @param key The key to search for
     * @return Cursor containing the data, or null if not found
     */
    fun getDataByKey(key: String): Cursor? {
        return try {
            val db = readableDatabase
            val cursor = db.query(
                DataContract.DataEntry.TABLE_NAME,
                null,
                "${DataContract.DataEntry.COLUMN_KEY} = ?",
                arrayOf(key),
                null,
                null,
                null
            )
            Log.d(TAG, "Queried data for key: $key, found ${cursor.count} rows")
            cursor
        } catch (e: Exception) {
            Log.e(TAG, "Error querying data for key: $key", e)
            null
        }
    }
    
    /**
     * Get all data
     * @return Cursor containing all data
     */
    fun getAllData(): Cursor? {
        return try {
            val db = readableDatabase
            val cursor = db.query(
                DataContract.DataEntry.TABLE_NAME,
                null,
                null,
                null,
                null,
                null,
                "${DataContract.DataEntry.COLUMN_TIMESTAMP} DESC"
            )
            Log.d(TAG, "Queried all data, found ${cursor.count} rows")
            cursor
        } catch (e: Exception) {
            Log.e(TAG, "Error querying all data", e)
            null
        }
    }
    
    /**
     * Delete data by key
     * @param key The key to delete
     * @return Number of rows deleted
     */
    fun deleteData(key: String): Int {
        return try {
            val db = writableDatabase
            val deletedRows = db.delete(
                DataContract.DataEntry.TABLE_NAME,
                "${DataContract.DataEntry.COLUMN_KEY} = ?",
                arrayOf(key)
            )
            Log.d(TAG, "Deleted $deletedRows rows for key: $key")
            deletedRows
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting data for key: $key", e)
            0
        }
    }

    /**
     * Check if a key exists in the database
     * @param key The key to check
     * @return true if the key exists, false otherwise
     */
    fun keyExists(key: String): Boolean {
        return try {
            val db = readableDatabase
            val cursor = db.query(
                DataContract.DataEntry.TABLE_NAME,
                arrayOf(DataContract.DataEntry.COLUMN_KEY),
                "${DataContract.DataEntry.COLUMN_KEY} = ?",
                arrayOf(key),
                null,
                null,
                null
            )

            val exists = cursor.use { it.count > 0 }
            Log.d(TAG, "Key '$key' exists: $exists")
            exists
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if key exists: $key", e)
            false
        }
    }

    /**
     * Get the count of total entries in the database
     * @return Number of entries
     */
    fun getEntryCount(): Int {
        return try {
            val db = readableDatabase
            val cursor = db.rawQuery("SELECT COUNT(*) FROM ${DataContract.DataEntry.TABLE_NAME}", null)

            cursor.use {
                if (it.moveToFirst()) {
                    val count = it.getInt(0)
                    Log.d(TAG, "Total entries in database: $count")
                    count
                } else {
                    0
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting entry count", e)
            0
        }
    }

    /**
     * Clear all data from the database
     * @return Number of rows deleted
     */
    fun clearAllData(): Int {
        return try {
            val db = writableDatabase
            val deletedRows = db.delete(DataContract.DataEntry.TABLE_NAME, null, null)
            Log.d(TAG, "Cleared all data, deleted $deletedRows rows")
            deletedRows
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all data", e)
            0
        }
    }
}
