package com.nrb.dms.database

/**
 * Data entity representing a key-value pair with timestamp
 */
data class DataEntity(
    val id: Long = 0,
    val key: String,
    val value: String,
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        /**
         * Validates if a key is valid (not empty, not null, reasonable length)
         */
        fun isValidKey(key: String?): Bo<PERSON>an {
            return !key.isNullOrBlank() && key.length <= 255
        }
        
        /**
         * Validates if a value is valid (not null, reasonable length)
         */
        fun isValidValue(value: String?): <PERSON><PERSON>an {
            return value != null && value.length <= 10000 // 10KB limit
        }
        
        /**
         * Sanitizes a key by trimming whitespace
         */
        fun sanitizeKey(key: String): String {
            return key.trim()
        }
        
        /**
         * Sanitizes a value by ensuring it's not null
         */
        fun sanitizeValue(value: String?): String {
            return value ?: ""
        }
    }
}
