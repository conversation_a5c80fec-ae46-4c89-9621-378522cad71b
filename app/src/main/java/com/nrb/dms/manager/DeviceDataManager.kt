package com.nrb.dms.manager

import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.provider.Settings
import android.util.Log
import com.nrb.dms.database.DataContract
import com.nrb.dms.model.DeviceInfo
import com.nrb.dms.utils.DeviceTypeUtil

/**
 * Manager class to handle storing device registration data in the DataContentProvider
 * This bridges the existing registration system with the new key-value data provider
 */
class DeviceDataManager(private val context: Context) {
    
    companion object {
        private const val TAG = "DeviceDataManager"
        
        // Standard keys for device registration data
        const val KEY_DEVICE_ID = "device_id"
        const val KEY_REGISTERED = "registered"
        const val KEY_REGISTRATION_TIMESTAMP = "registration_timestamp"
        const val KEY_DEVICE_TYPE = "device_type"
        const val KEY_DEVICE_NAME = "device_name"
        const val KEY_DEVICE_MODEL = "device_model"
        const val KEY_MANUFACTURER = "manufacturer"
        const val KEY_ANDROID_VERSION = "android_version"
        const val KEY_API_LEVEL = "api_level"
        const val KEY_BRAND = "brand"
        const val KEY_PRODUCT = "product"
        const val KEY_HARDWARE = "hardware"
        const val KEY_FINGERPRINT = "fingerprint"
        const val KEY_LOCALE = "locale"
        const val KEY_TIMEZONE = "timezone"
    }
    
    // Removed PreferencesManager dependency to avoid circular dependency
    
    /**
     * Store device registration data in the DataContentProvider after successful registration
     * @param deviceInfo The device information from registration
     * @param deviceId The device ID returned from the server (should be Android ID)
     * @param timestamp The registration timestamp
     */
    fun storeDeviceRegistrationData(deviceInfo: DeviceInfo, deviceId: String, timestamp: Long) {
        Log.d(TAG, "Storing device registration data in DataContentProvider")
        
        try {
            // Store core registration data
            storeKeyValue(KEY_DEVICE_ID, deviceId)
            storeKeyValue(KEY_REGISTERED, "true")
            storeKeyValue(KEY_REGISTRATION_TIMESTAMP, timestamp.toString())
            
            // Store device information
            storeKeyValue(KEY_DEVICE_TYPE, deviceInfo.deviceType)
            storeKeyValue(KEY_DEVICE_NAME, deviceInfo.deviceName)
            storeKeyValue(KEY_DEVICE_MODEL, deviceInfo.deviceModel)
            storeKeyValue(KEY_MANUFACTURER, deviceInfo.manufacturer)
            storeKeyValue(KEY_ANDROID_VERSION, deviceInfo.androidVersion)
            storeKeyValue(KEY_API_LEVEL, deviceInfo.apiLevel.toString())
            storeKeyValue(KEY_BRAND, deviceInfo.brand)
            storeKeyValue(KEY_PRODUCT, deviceInfo.product)
            storeKeyValue(KEY_HARDWARE, deviceInfo.hardware)
            storeKeyValue(KEY_FINGERPRINT, deviceInfo.fingerprint)
            storeKeyValue(KEY_LOCALE, deviceInfo.locale)
            storeKeyValue(KEY_TIMEZONE, deviceInfo.timeZone)
            
            Log.d(TAG, "Successfully stored device registration data")
        } catch (e: Exception) {
            Log.e(TAG, "Error storing device registration data", e)
        }
    }
    
    /**
     * Update registration status in the DataContentProvider
     * @param isRegistered Whether the device is registered
     */
    fun updateRegistrationStatus(isRegistered: Boolean) {
        Log.d(TAG, "Updating registration status to: $isRegistered")
        
        try {
            storeKeyValue(KEY_REGISTERED, isRegistered.toString())
            
            if (isRegistered) {
                // Also update current device information when marking as registered
                val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
                val timestamp = System.currentTimeMillis() // Use current time if not provided
                val deviceType = DeviceTypeUtil.getDeviceTypeString(context)

                if (!deviceId.isNullOrEmpty()) {
                    storeKeyValue(KEY_DEVICE_ID, deviceId)
                    storeKeyValue(KEY_REGISTRATION_TIMESTAMP, timestamp.toString())
                    storeKeyValue(KEY_DEVICE_TYPE, deviceType)
                }
            }
            
            Log.d(TAG, "Successfully updated registration status")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating registration status", e)
        }
    }
    
    /**
     * Clear device registration data from the DataContentProvider
     * This is called when registration is reset or cleared
     */
    fun clearDeviceRegistrationData() {
        Log.d(TAG, "Clearing device registration data from DataContentProvider")
        
        try {
            // Delete all device-related keys
            val keysToDelete = listOf(
                KEY_DEVICE_ID, KEY_REGISTERED, KEY_REGISTRATION_TIMESTAMP,
                KEY_DEVICE_TYPE, KEY_DEVICE_NAME, KEY_DEVICE_MODEL,
                KEY_MANUFACTURER, KEY_ANDROID_VERSION, KEY_API_LEVEL,
                KEY_BRAND, KEY_PRODUCT, KEY_HARDWARE, KEY_FINGERPRINT,
                KEY_LOCALE, KEY_TIMEZONE
            )
            
            keysToDelete.forEach { key ->
                deleteKey(key)
            }
            
            Log.d(TAG, "Successfully cleared device registration data")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing device registration data", e)
        }
    }
    
    /**
     * Store a key-value pair in the DataContentProvider
     * @param key The key to store
     * @param value The value to store
     */
    private fun storeKeyValue(key: String, value: String) {
        val values = ContentValues().apply {
            put(DataContract.DataEntry.COLUMN_KEY, key)
            put(DataContract.DataEntry.COLUMN_VALUE, value)
        }
        
        val uri = context.contentResolver.insert(
            Uri.parse(DataContract.DataEntry.CONTENT_URI),
            values
        )
        
        if (uri != null) {
            Log.d(TAG, "Stored key-value pair: $key = $value")
        } else {
            Log.w(TAG, "Failed to store key-value pair: $key = $value")
        }
    }
    
    /**
     * Delete a key from the DataContentProvider
     * @param key The key to delete
     */
    private fun deleteKey(key: String) {
        val uri = Uri.parse("${DataContract.DataEntry.CONTENT_URI}/$key")
        val deletedRows = context.contentResolver.delete(uri, null, null)
        
        if (deletedRows > 0) {
            Log.d(TAG, "Deleted key: $key")
        } else {
            Log.d(TAG, "Key not found or already deleted: $key")
        }
    }
}
