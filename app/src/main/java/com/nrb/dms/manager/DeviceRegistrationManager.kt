package com.nrb.dms.manager

import android.content.Context
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import com.nrb.dms.api.RetrofitClient
import com.nrb.dms.model.DeviceInfo
import com.nrb.dms.model.DeviceRegistrationResponse
import com.nrb.dms.utils.DeviceTypeUtil
import com.nrb.dms.utils.PreferencesManager
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * Manager class to handle device registration process
 */
class DeviceRegistrationManager(private val context: Context) {

    private val TAG = "DeviceRegistrationManager"
    private val preferencesManager = PreferencesManager.getInstance(context)
    private val scheduler: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor()

    init {
        // Initialize RetrofitClient
        RetrofitClient.initialize(context)
    }

    /**
     * Check if device is registered and register if not
     * @return true if device is already registered, false otherwise
     */
    fun checkAndRegisterDevice(): Boolean {
        // Get Android ID as the Device ID
        val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)

        if (androidId != null && androidId.isNotEmpty()) {
            // We have Android ID, try to verify it with the server
            Log.d(TAG, "Found Android ID (Device ID): $androidId, verifying with server")
            if (verifyDeviceWithServer(androidId)) {
                // Device verified with server
                Log.d(TAG, "Device verified with server, Android ID: $androidId")
                return true
            } else {
                // Server doesn't recognize this Android ID, need to register
                Log.d(TAG, "Server doesn't recognize Android ID: $androidId, registering")
                registerDevice()
                return false
            }
        } else {
            // No Android ID available (shouldn't happen on normal devices)
            Log.e(TAG, "Android ID not available, cannot register device")
            return false
        }
    }

    /**
     * Verify device with server using stored device ID
     * @param deviceId The device ID to verify
     * @return true if device exists on server, false otherwise
     */
    private fun verifyDeviceWithServer(deviceId: String): Boolean {
        try {
            val response = RetrofitClient.deviceRegistrationApi.getDeviceById(deviceId).execute()

            if (response.isSuccessful && response.body() != null) {
                val deviceResponse = response.body()!!
                if (deviceResponse.success && deviceResponse.deviceId != null) {
                    // Device exists on server, ensure it's marked as registered locally
                    preferencesManager.setDeviceRegistered(deviceResponse.deviceId)
                    return true
                }
            }

            // If we get here, the device wasn't found or there was an error
            Log.d(TAG, "Device verification failed: ${response.code()} ${response.message()}")
            return false
        } catch (e: Exception) {
            // Network error or other exception
            Log.e(TAG, "Error verifying device with server", e)
            // Return true if we're offline - we'll assume the device is registered
            // and continue using the stored ID until we can verify with the server
            return e is java.net.UnknownHostException || e is java.net.SocketTimeoutException
        }
    }

    /**
     * Collect device information and send registration request
     */
    private fun registerDevice() {
        val deviceInfo = collectDeviceInfo()
        sendRegistrationRequest(deviceInfo)
    }

    /**
     * Collect device information
     */
    private fun collectDeviceInfo(): DeviceInfo {
        // Information accessible without special permissions
        val deviceModel = Build.MODEL
        val manufacturer = Build.MANUFACTURER
        val androidVersion = Build.VERSION.RELEASE
        val apiLevel = Build.VERSION.SDK_INT
        val brand = Build.BRAND
        val product = Build.PRODUCT
        val hardware = Build.HARDWARE
        val fingerprint = Build.FINGERPRINT
        val locale = Locale.getDefault().toString()
        val timeZone = TimeZone.getDefault().id
        val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        val deviceType = DeviceTypeUtil.getDeviceTypeString(context)
        val deviceName = Build.DEVICE
        val registrationTimestamp = preferencesManager.getRegistrationTimestamp()

        // Log device information for debugging
        Log.d(TAG, "Device type detected: $deviceType")
        Log.d(TAG, "Device name: $deviceName")
        Log.d(TAG, "Android ID (Device ID): $androidId")
        Log.d(TAG, "Registration timestamp: $registrationTimestamp")

        return DeviceInfo(
            deviceModel = deviceModel,
            deviceType = deviceType,
            manufacturer = manufacturer,
            androidVersion = androidVersion,
            apiLevel = apiLevel,
            brand = brand,
            product = product,
            hardware = hardware,
            fingerprint = fingerprint,
            locale = locale,
            timeZone = timeZone,
            androidId = androidId,
            deviceName = deviceName,
            registrationTimestamp = registrationTimestamp
        )
    }

    /**
     * Send registration request to the API
     */
    private fun sendRegistrationRequest(deviceInfo: DeviceInfo) {
        val serverUrl = PreferencesManager.getInstance(context).getServerUrl()
        Log.d(TAG, "Sending device registration request to server: $serverUrl")
        Log.d(TAG, "Device info: Model=${deviceInfo.deviceModel}, Type=${deviceInfo.deviceType}, Manufacturer=${deviceInfo.manufacturer}, Name=${deviceInfo.deviceName}, Timestamp=${deviceInfo.registrationTimestamp}")

        RetrofitClient.deviceRegistrationApi.registerDevice(deviceInfo).enqueue(object : Callback<DeviceRegistrationResponse> {
            override fun onResponse(call: Call<DeviceRegistrationResponse>, response: Response<DeviceRegistrationResponse>) {
                // Check for status code 201 (Created) which indicates successful registration
                if (response.code() == 201 && response.body() != null) {
                    val registrationResponse = response.body()!!
                    Log.d(TAG, "Registration response: success=${registrationResponse.success}, message=${registrationResponse.message}, deviceId=${registrationResponse.deviceId}")

                    if (registrationResponse.deviceId != null) {
                        // Registration successful with status 201
                        val currentTime = System.currentTimeMillis()

                        // Use the timestamp from the server response if available, otherwise use current time
                        val timestamp = registrationResponse.data?.registrationTimestamp ?: currentTime
                        preferencesManager.setDeviceRegistered(registrationResponse.deviceId, timestamp)

                        // Log the full response data if available
                        registrationResponse.data?.let {
                            Log.d(TAG, "Received full device data from server: Model=${it.deviceModel}, Type=${it.deviceType}, Name=${it.deviceName}, Timestamp=${it.registrationTimestamp}")
                        }

                        Log.d(TAG, "Device registered successfully with ID: ${registrationResponse.deviceId} at timestamp: $timestamp")
                        Toast.makeText(context, "Device registered successfully", Toast.LENGTH_SHORT).show()
                    } else {
                        // Registration response is missing device ID
                        Log.e(TAG, "Registration response missing device ID: ${registrationResponse.message}")
                        Toast.makeText(context, "Device registration failed: Missing device ID", Toast.LENGTH_SHORT).show()
                        scheduleRetry()
                    }
                } else if (response.isSuccessful && response.body() != null) {
                    val registrationResponse = response.body()!!
                    Log.d(TAG, "Registration response: success=${registrationResponse.success}, message=${registrationResponse.message}, deviceId=${registrationResponse.deviceId}")

                    if (registrationResponse.success && registrationResponse.deviceId != null) {
                        // Registration successful
                        val timestamp = registrationResponse.data?.registrationTimestamp ?: System.currentTimeMillis()
                        preferencesManager.setDeviceRegistered(registrationResponse.deviceId, timestamp)
                        Log.d(TAG, "Device registered successfully with ID: ${registrationResponse.deviceId} at timestamp: $timestamp")
                        Toast.makeText(context, "Device registered successfully", Toast.LENGTH_SHORT).show()
                    } else {
                        // Registration failed with API error
                        Log.e(TAG, "Registration failed: ${registrationResponse.message}")
                        Toast.makeText(context, "Device registration failed: ${registrationResponse.message}", Toast.LENGTH_SHORT).show()
                        scheduleRetry()
                    }
                } else {
                    // Registration failed with HTTP error
                    try {
                        val errorBody = response.errorBody()?.string() ?: "No error body"
                        Log.e(TAG, "Registration failed with HTTP error: ${response.code()}, Error: $errorBody")
                        Toast.makeText(context, "Device registration failed: HTTP error ${response.code()}", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing error response", e)
                        Log.e(TAG, "Registration failed with HTTP error: ${response.code()}")
                        Toast.makeText(context, "Device registration failed: HTTP error ${response.code()}", Toast.LENGTH_SHORT).show()
                    }
                    scheduleRetry()
                }
            }

            override fun onFailure(call: Call<DeviceRegistrationResponse>, t: Throwable) {
                // Registration failed with network error
                Log.e(TAG, "Registration failed with network error: ${t.javaClass.simpleName}: ${t.message}", t)
                val errorMessage = when (t) {
                    is java.net.UnknownHostException -> "Server not found. Check your internet connection."
                    is java.net.SocketTimeoutException -> "Connection timed out. Please try again later."
                    is javax.net.ssl.SSLHandshakeException -> "SSL certificate error. Please check your connection."
                    else -> "Network error: ${t.message}"
                }
                Toast.makeText(context, "Device registration failed: $errorMessage", Toast.LENGTH_SHORT).show()
                scheduleRetry()
            }
        })
    }

    /**
     * Schedule a retry for failed registration
     */
    private fun scheduleRetry() {
        Log.d(TAG, "Scheduling registration retry in 5 minutes")
        scheduler.schedule({ registerDevice() }, 5, TimeUnit.MINUTES)
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        scheduler.shutdown()
    }
}
