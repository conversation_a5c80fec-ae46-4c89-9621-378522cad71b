package com.nrb.dms

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.drawerlayout.widget.DrawerLayout
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.navigation.NavigationView
import com.google.android.material.snackbar.Snackbar
import com.nrb.dms.api.RetrofitClient
import com.nrb.dms.databinding.ActivityMainBinding
import com.nrb.dms.manager.DeviceRegistrationManager
import com.nrb.dms.utils.PreferencesManager
import android.util.Log

class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding

    // Activity result launcher for settings
    private val settingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // Refresh RetrofitClient with new URL
            RetrofitClient.resetClient()
        }
    }

    // Activity result launcher for content provider test
    private val contentProviderTestLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { /* No result handling needed */ }
    private lateinit var deviceRegistrationManager: DeviceRegistrationManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize RetrofitClient
        RetrofitClient.initialize(this)

        setSupportActionBar(binding.appBarMain.toolbar)


        val drawerLayout: DrawerLayout = binding.drawerLayout
        val navView: NavigationView = binding.navView
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        // Passing each menu ID as a set of Ids because each
        // menu should be considered as top level destinations.
        // Create a set of top level destinations using resource ID
        val topLevelDestinations = setOf(R.id.nav_home)

        // Create the AppBarConfiguration
        appBarConfiguration = AppBarConfiguration(topLevelDestinations, drawerLayout)
        setupActionBarWithNavController(navController, appBarConfiguration)
        navView.setupWithNavController(navController)

        // Set up custom navigation item click listener
        navView.setNavigationItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.nav_home -> {
                    navController.navigate(R.id.nav_home)
                    drawerLayout.closeDrawers()
                    true
                }
                R.id.nav_test_provider -> {
                    // Launch the ContentProviderTestActivity
                    contentProviderTestLauncher.launch(Intent(this, ContentProviderTestActivity::class.java))
                    drawerLayout.closeDrawers()
                    true
                }
                else -> false
            }
        }

        // Initialize device registration manager
        deviceRegistrationManager = DeviceRegistrationManager(this)

        // Broadcast device availability when MainActivity starts
        broadcastDeviceAvailability()
    }

    private fun broadcastDeviceAvailability() {
        try {
            val preferencesManager = PreferencesManager.getInstance(this)
            val deviceId = preferencesManager.getDeviceId()
            val isRegistered = preferencesManager.isDeviceRegistered()

            // Send DMS availability broadcast
            val availabilityIntent = Intent("com.nrb.dms.action.DMS_AVAILABLE").apply {
                putExtra("provider_authority", "com.nrb.dms.provider")
                putExtra("device_id", deviceId ?: "unknown")
                putExtra("registration_status", isRegistered)
            }
            sendBroadcast(availabilityIntent)

            // Send device status broadcast
            val statusIntent = Intent("com.nrb.dms.action.DEVICE_REGISTERED").apply {
                putExtra("device_id", deviceId ?: "unknown")
                putExtra("provider_authority", "com.nrb.dms.provider")
                putExtra("is_registered", isRegistered)
                putExtra("registration_timestamp", preferencesManager.getRegistrationTimestamp())
            }
            sendBroadcast(statusIntent)

            Log.d("MainActivity", "Broadcasted device availability - ID: $deviceId, Registered: $isRegistered")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error broadcasting device availability", e)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                // Launch settings activity
                settingsLauncher.launch(Intent(this, SettingsActivity::class.java))
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up resources
        deviceRegistrationManager.cleanup()
    }
}
    