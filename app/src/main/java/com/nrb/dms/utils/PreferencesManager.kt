package com.nrb.dms.utils

import android.content.Context
import android.content.SharedPreferences
import android.provider.Settings

/**
 * Utility class to manage SharedPreferences operations
 */
class PreferencesManager(private val context: Context) {

    companion object {
        private const val PREF_NAME = "dms_preferences"
        private const val KEY_DEVICE_REGISTERED = "device_registered"
        private const val KEY_REGISTRATION_TIMESTAMP = "registration_timestamp"
        private const val KEY_DEVICE_ID = "device_id"  // Keep for backward compatibility
        private const val KEY_SERVER_URL = "server_url"

        // Default server URL
        private const val DEFAULT_SERVER_URL = "http://172.16.11.70/"

        @Volatile
        private var INSTANCE: PreferencesManager? = null

        fun getInstance(context: Context): PreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PreferencesManager(context.applicationContext).also {
                    INSTANCE = it
                }
            }
        }
    }

    private val preferences: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

    /**
     * Check if the device is already registered
     */
    fun isDeviceRegistered(): Boolean {
        return preferences.getBoolean(KEY_DEVICE_REGISTERED, false)
    }

    /**
     * Mark the device as registered
     * @param deviceId The device ID received from the server (should be Android ID)
     * @param timestamp Optional timestamp to use instead of current time
     */
    fun setDeviceRegistered(deviceId: String, timestamp: Long = System.currentTimeMillis()) {
        preferences.edit().apply {
            putBoolean(KEY_DEVICE_REGISTERED, true)
            putLong(KEY_REGISTRATION_TIMESTAMP, timestamp)
            // Store the device ID for backward compatibility, but we'll always use Android ID
            putString(KEY_DEVICE_ID, deviceId)
            apply()
        }
    }

    /**
     * Get the device ID - Returns Android ID as the Device ID
     */
    fun getDeviceId(): String? {
        // Always return Android ID as the Device ID
        return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    }

    /**
     * Get the registration timestamp
     */
    fun getRegistrationTimestamp(): Long {
        return preferences.getLong(KEY_REGISTRATION_TIMESTAMP, 0)
    }

    /**
     * Clear registration status (for testing or reset purposes)
     */
    fun clearRegistrationStatus() {
        preferences.edit().apply {
            remove(KEY_DEVICE_REGISTERED)
            remove(KEY_REGISTRATION_TIMESTAMP)
            remove(KEY_DEVICE_ID)
            apply()
        }
    }

    /**
     * Get the server URL
     * @return The server URL or the default URL if not set
     */
    fun getServerUrl(): String {
        return preferences.getString(KEY_SERVER_URL, DEFAULT_SERVER_URL) ?: DEFAULT_SERVER_URL
    }

    /**
     * Set the server URL
     * @param url The server URL to set
     */
    fun setServerUrl(url: String) {
        preferences.edit().putString(KEY_SERVER_URL, url).apply()
    }

    /**
     * Reset the server URL to the default value
     */
    fun resetServerUrl() {
        preferences.edit().remove(KEY_SERVER_URL).apply()
    }
}
