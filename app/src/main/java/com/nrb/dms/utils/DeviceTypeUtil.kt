package com.nrb.dms.utils

import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.os.Build

/**
 * Utility class to detect device type (phone, tablet, foldable)
 */
object DeviceTypeUtil {

    /**
     * Device type enum
     */
    enum class DeviceType {
        PHONE,
        TABLET,
        FOLDABLE,
        UNKNOWN
    }

    /**
     * Detect the device type based on screen size and hardware features
     * @param context Application context
     * @return DeviceType enum value
     */
    fun getDeviceType(context: Context): DeviceType {
        // Check if device has a hinge sensor (indicating it's a foldable)
        val hasFoldableFeature = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.packageManager.hasSystemFeature(PackageManager.FEATURE_SENSOR_HINGE_ANGLE)
        } else {
            false
        }

        if (hasFoldableFeature) {
            return DeviceType.FOLDABLE
        }

        // Check screen size to determine if it's a tablet
        val isTablet = isTabletBasedOnScreenSize(context)
        return if (isTablet) DeviceType.TABLET else DeviceType.PHONE
    }

    /**
     * Check if device is a tablet based on screen size
     * @param context Application context
     * @return true if device is a tablet, false otherwise
     */
    private fun isTabletBasedOnScreenSize(context: Context): Boolean {
        // Devices with smallest width >= 600dp are considered tablets
        return (context.resources.configuration.screenLayout and 
                Configuration.SCREENLAYOUT_SIZE_MASK) >= 
                Configuration.SCREENLAYOUT_SIZE_LARGE
    }

    /**
     * Get device type as a string for display or API purposes
     * @param context Application context
     * @return String representation of device type
     */
    fun getDeviceTypeString(context: Context): String {
        return when (getDeviceType(context)) {
            DeviceType.PHONE -> "phone"
            DeviceType.TABLET -> "tablet"
            DeviceType.FOLDABLE -> "foldable"
            DeviceType.UNKNOWN -> "unknown"
        }
    }
}
