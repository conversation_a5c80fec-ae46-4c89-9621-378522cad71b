package com.nrb.dms

import android.os.Bundle
import android.util.Patterns
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.nrb.dms.api.RetrofitClient
import com.nrb.dms.databinding.ActivitySettingsBinding
import com.nrb.dms.utils.PreferencesManager

class SettingsActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySettingsBinding
    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Set up back button in action bar
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)

        // Initialize preferences manager
        preferencesManager = PreferencesManager.getInstance(this)

        // Load current URL
        binding.serverUrlEdit.setText(preferencesManager.getServerUrl())

        // Set up save button
        binding.saveButton.setOnClickListener {
            saveServerUrl()
        }

        // Set up reset button
        binding.resetButton.setOnClickListener {
            resetServerUrl()
        }
    }

    private fun saveServerUrl() {
        val url = binding.serverUrlEdit.text.toString().trim()

        // Validate URL format
        if (!isValidUrl(url)) {
            binding.serverUrlLayout.error = getString(R.string.invalid_url)
            return
        }

        // Clear any previous errors
        binding.serverUrlLayout.error = null

        // Ensure URL ends with a slash
        val formattedUrl = if (url.endsWith("/")) url else "$url/"

        // Save URL to preferences
        preferencesManager.setServerUrl(formattedUrl)

        // Reset Retrofit client to use the new URL
        RetrofitClient.resetClient()

        // Show success message
        Toast.makeText(this, R.string.url_saved, Toast.LENGTH_SHORT).show()

        // Set result to indicate settings were changed
        setResult(RESULT_OK)
    }

    private fun resetServerUrl() {
        // Reset URL to default
        preferencesManager.resetServerUrl()

        // Update UI
        binding.serverUrlEdit.setText(preferencesManager.getServerUrl())

        // Reset Retrofit client to use the default URL
        RetrofitClient.resetClient()

        // Show success message
        Toast.makeText(this, R.string.url_reset, Toast.LENGTH_SHORT).show()

        // Set result to indicate settings were changed
        setResult(RESULT_OK)
    }

    private fun isValidUrl(url: String): Boolean {
        // Check if it's a valid URL pattern
        if (!Patterns.WEB_URL.matcher(url).matches()) {
            return false
        }

        // Check if it starts with http:// or https://
        return url.startsWith("http://") || url.startsWith("https://")
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
