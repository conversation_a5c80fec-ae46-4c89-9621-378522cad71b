package com.nrb.dms

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.nrb.dms.api.RetrofitClient
import com.nrb.dms.databinding.ActivityStatusBinding
import com.nrb.dms.manager.DeviceRegistrationManager
import com.nrb.dms.receiver.DMSAvailabilityReceiver
import com.nrb.dms.utils.PreferencesManager
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class StatusActivity : AppCompatActivity() {

    private lateinit var binding: ActivityStatusBinding
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var deviceRegistrationManager: DeviceRegistrationManager

    // Activity result launcher for settings
    private val settingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // Update server URL display
            binding.serverUrlText.text = getString(R.string.server_url_display, preferencesManager.getServerUrl())
        }
    }
    private val handler = Handler(Looper.getMainLooper())

    companion object {
        private const val DISPLAY_DURATION_MS = 3000L // 3 seconds
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStatusBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize preferences and RetrofitClient
        preferencesManager = PreferencesManager.getInstance(this)
        RetrofitClient.initialize(this)

        // Display current server URL
        binding.serverUrlText.text = getString(R.string.server_url_display, preferencesManager.getServerUrl())

        deviceRegistrationManager = DeviceRegistrationManager(this)

        // Always broadcast device availability when app starts
        broadcastDeviceAvailability()

        checkRegistrationStatus()

        binding.retryButton.setOnClickListener {
            retryRegistration()
        }

        // Set up settings button
        binding.settingsButton.setOnClickListener {
            settingsLauncher.launch(Intent(this, SettingsActivity::class.java))
        }

        // Set up long click on timestamp to copy device ID
        binding.timestampText.setOnLongClickListener {
            copyDeviceIdToClipboard()
            true
        }
    }

    private fun checkRegistrationStatus() {
        if (preferencesManager.isDeviceRegistered()) {
            // Device is registered - show green checkmark
            showRegisteredStatus()

            // Automatically proceed to main activity after delay
            handler.postDelayed({ navigateToMainActivity() }, DISPLAY_DURATION_MS)
        } else {
            // Device is not registered - show red X
            showNotRegisteredStatus()
        }
    }

    private fun showRegisteredStatus() {
        binding.statusIcon.setImageResource(R.drawable.ic_checkmark)
        binding.statusText.text = getString(R.string.device_registered)
        binding.statusText.setTextColor(getColor(R.color.status_success))

        // Get and display Device ID (which is Android ID)
        val deviceId = preferencesManager.getDeviceId()

        val deviceIdText = if (deviceId != null && deviceId.isNotEmpty()) {
            "Device ID: $deviceId"
        } else {
            "Device ID: Not Available"
        }

        // Format and display registration timestamp with Device ID
        val timestamp = preferencesManager.getRegistrationTimestamp()
        val dateFormat = SimpleDateFormat("MMM dd, yyyy hh:mm a", Locale.getDefault())
        val formattedDate = dateFormat.format(Date(timestamp))

        // Show timestamp and Device ID
        binding.timestampText.text = "${getString(R.string.registered_on, formattedDate)}\n\n$deviceIdText"

        // Hide retry button
        binding.retryButton.visibility = View.GONE
    }

    private fun showNotRegisteredStatus() {
        binding.statusIcon.setImageResource(R.drawable.ic_error)
        binding.statusText.text = getString(R.string.device_not_registered)
        binding.statusText.setTextColor(getColor(R.color.status_error))

        // Show Device ID even when not registered
        val deviceId = preferencesManager.getDeviceId()

        val deviceIdText = if (deviceId != null && deviceId.isNotEmpty()) {
            "Device ID: $deviceId"
        } else {
            "Device ID: Not Available"
        }

        binding.timestampText.text = deviceIdText

        // Show retry button
        binding.retryButton.visibility = View.VISIBLE
    }

    private fun retryRegistration() {
        // Disable retry button while attempting registration
        binding.retryButton.isEnabled = false

        // Attempt to register the device
        deviceRegistrationManager.checkAndRegisterDevice()

        // Check status again after a short delay
        handler.postDelayed({
            if (preferencesManager.isDeviceRegistered()) {
                showRegisteredStatus()
                handler.postDelayed({ navigateToMainActivity() }, DISPLAY_DURATION_MS)
            } else {
                // Re-enable retry button if registration failed
                binding.retryButton.isEnabled = true
                // Ensure device ID container is hidden if registration failed
                binding.deviceIdContainer.visibility = View.GONE
            }
        }, 2000) // Check after 2 seconds
    }

    private fun copyDeviceIdToClipboard() {
        val deviceId = preferencesManager.getDeviceId()

        if (deviceId != null && deviceId.isNotEmpty()) {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("Device ID", deviceId)
            clipboard.setPrimaryClip(clip)

            Toast.makeText(this, "Device ID copied: $deviceId", Toast.LENGTH_LONG).show()
        } else {
            Toast.makeText(this, "Device ID not available", Toast.LENGTH_SHORT).show()
        }
    }

    private fun broadcastDeviceAvailability() {
        try {
            // Broadcast device availability immediately when app starts
            val deviceId = preferencesManager.getDeviceId()
            val isRegistered = preferencesManager.isDeviceRegistered()

            // Send DMS availability broadcast
            val availabilityIntent = Intent("com.nrb.dms.action.DMS_AVAILABLE").apply {
                putExtra("provider_authority", "com.nrb.dms.provider")
                putExtra("device_id", deviceId ?: "unknown")
                putExtra("registration_status", isRegistered)
            }
            sendBroadcast(availabilityIntent)

            // Send device status broadcast
            val statusIntent = Intent("com.nrb.dms.action.DEVICE_REGISTERED").apply {
                putExtra("device_id", deviceId ?: "unknown")
                putExtra("provider_authority", "com.nrb.dms.provider")
                putExtra("is_registered", isRegistered)
                putExtra("registration_timestamp", preferencesManager.getRegistrationTimestamp())
            }
            sendBroadcast(statusIntent)

            Log.d("StatusActivity", "Broadcasted device availability - ID: $deviceId, Registered: $isRegistered")
        } catch (e: Exception) {
            Log.e("StatusActivity", "Error broadcasting device availability", e)
        }
    }

    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                // Launch settings activity
                settingsLauncher.launch(Intent(this, SettingsActivity::class.java))
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
    }
}
