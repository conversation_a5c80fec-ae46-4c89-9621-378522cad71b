package com.nrb.dms.provider

import android.content.ContentProvider
import android.content.ContentUris
import android.content.ContentValues
import android.content.UriMatcher
import android.database.Cursor
import android.net.Uri
import android.util.Log
import com.nrb.dms.database.DataContract
import com.nrb.dms.database.DataDbHelper
import com.nrb.dms.database.DataEntity

/**
 * ContentProvider for exposing key-value data to external apps
 */
class DataContentProvider : ContentProvider() {
    
    companion object {
        private const val TAG = "DataContentProvider"
        
        // URI matcher
        private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH).apply {
            addURI(DataContract.AUTHORITY, DataContract.PATH_DATA, DataContract.DATA)
            addURI(DataContract.AUTHORITY, "${DataContract.PATH_DATA}/*", DataContract.DATA_WITH_KEY)
        }
    }
    
    private lateinit var dbHelper: DataDbHelper
    
    override fun onCreate(): Boolean {
        context?.let {
            dbHelper = DataDbHelper(it)
            return true
        }
        return false
    }
    
    override fun query(
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?
    ): Cursor? {
        Log.d(TAG, "Query called with URI: $uri")
        
        return try {
            when (uriMatcher.match(uri)) {
                DataContract.DATA -> {
                    // Query all data
                    dbHelper.getAllData()
                }
                DataContract.DATA_WITH_KEY -> {
                    // Query specific key
                    val key = uri.lastPathSegment
                    if (key.isNullOrBlank()) {
                        Log.w(TAG, "Invalid key in URI: $uri")
                        null
                    } else {
                        dbHelper.getDataByKey(key)
                    }
                }
                else -> {
                    Log.w(TAG, "Unknown URI: $uri")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error querying data", e)
            null
        }
    }
    
    override fun getType(uri: Uri): String? {
        return when (uriMatcher.match(uri)) {
            DataContract.DATA -> DataContract.DataEntry.CONTENT_TYPE
            DataContract.DATA_WITH_KEY -> DataContract.DataEntry.CONTENT_ITEM_TYPE
            else -> null
        }
    }
    
    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        Log.d(TAG, "Insert called with URI: $uri")
        
        if (uriMatcher.match(uri) != DataContract.DATA) {
            Log.w(TAG, "Insert not supported for URI: $uri")
            return null
        }
        
        values ?: return null
        
        return try {
            val key = values.getAsString(DataContract.DataEntry.COLUMN_KEY)
            val value = values.getAsString(DataContract.DataEntry.COLUMN_VALUE)
            
            // Validate input
            if (!DataEntity.isValidKey(key)) {
                Log.w(TAG, "Invalid key: $key")
                return null
            }
            
            if (!DataEntity.isValidValue(value)) {
                Log.w(TAG, "Invalid value for key: $key")
                return null
            }
            
            // Sanitize input
            val sanitizedKey = DataEntity.sanitizeKey(key)
            val sanitizedValue = DataEntity.sanitizeValue(value)
            
            val id = dbHelper.insertOrUpdateData(sanitizedKey, sanitizedValue)
            
            if (id > 0) {
                val resultUri = ContentUris.withAppendedId(Uri.parse(DataContract.DataEntry.CONTENT_URI), id)
                context?.contentResolver?.notifyChange(uri, null)
                resultUri
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting data", e)
            null
        }
    }
    
    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        Log.d(TAG, "Delete called with URI: $uri")
        
        return try {
            when (uriMatcher.match(uri)) {
                DataContract.DATA_WITH_KEY -> {
                    val key = uri.lastPathSegment
                    if (key.isNullOrBlank()) {
                        Log.w(TAG, "Invalid key in URI: $uri")
                        0
                    } else {
                        val deletedRows = dbHelper.deleteData(key)
                        if (deletedRows > 0) {
                            context?.contentResolver?.notifyChange(uri, null)
                        }
                        deletedRows
                    }
                }
                else -> {
                    Log.w(TAG, "Delete not supported for URI: $uri")
                    0
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting data", e)
            0
        }
    }
    
    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        Log.d(TAG, "Update called with URI: $uri")
        
        if (uriMatcher.match(uri) != DataContract.DATA_WITH_KEY) {
            Log.w(TAG, "Update not supported for URI: $uri")
            return 0
        }
        
        values ?: return 0
        
        return try {
            val key = uri.lastPathSegment
            val value = values.getAsString(DataContract.DataEntry.COLUMN_VALUE)
            
            if (key.isNullOrBlank() || !DataEntity.isValidKey(key)) {
                Log.w(TAG, "Invalid key: $key")
                return 0
            }
            
            if (!DataEntity.isValidValue(value)) {
                Log.w(TAG, "Invalid value for key: $key")
                return 0
            }
            
            val sanitizedValue = DataEntity.sanitizeValue(value)
            val id = dbHelper.insertOrUpdateData(key, sanitizedValue)
            
            if (id > 0) {
                context?.contentResolver?.notifyChange(uri, null)
                1
            } else {
                0
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating data", e)
            0
        }
    }
}
