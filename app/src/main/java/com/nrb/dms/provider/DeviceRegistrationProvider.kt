package com.nrb.dms.provider

import android.content.ContentProvider
import android.content.ContentValues
import android.content.UriMatcher
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import android.provider.Settings
import android.util.Log
import com.nrb.dms.utils.DeviceTypeUtil
import com.nrb.dms.utils.PreferencesManager

/**
 * ContentProvider that exposes device registration information to other apps
 */
class DeviceRegistrationProvider : ContentProvider() {

    companion object {
        private const val TAG = "DeviceRegistrationProvider"

        // Authority must match the one declared in the manifest
        const val AUTHORITY = "com.nrb.dms.provider"

        // Path for device ID
        const val PATH_DEVICE_ID = "device_id"

        // URI for device ID
        val CONTENT_URI: Uri = Uri.parse("content://$AUTHORITY/$PATH_DEVICE_ID")

        // Column names
        const val COLUMN_DEVICE_ID = "device_id"  // This IS the Android ID
        const val COLUMN_REGISTERED = "registered"
        const val COLUMN_TIMESTAMP = "timestamp"
        const val COLUMN_DEVICE_TYPE = "device_type"
        const val COLUMN_DEVICE_NAME = "device_name"

        // URI matcher codes
        private const val CODE_DEVICE_ID = 1

        // URI matcher
        private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH).apply {
            addURI(AUTHORITY, PATH_DEVICE_ID, CODE_DEVICE_ID)
        }
    }

    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(): Boolean {
        Log.d(TAG, "onCreate: Initializing DeviceRegistrationProvider")
        try {
            preferencesManager = PreferencesManager.getInstance(context!!)
            Log.d(TAG, "onCreate: Successfully initialized PreferencesManager")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "onCreate: Error initializing DeviceRegistrationProvider", e)
            return false
        }
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        Log.d(TAG, "query: Received query for URI: $uri")
        try {
            val match = uriMatcher.match(uri)
            Log.d(TAG, "query: URI matcher result: $match")

            return when (match) {
                CODE_DEVICE_ID -> {
                    Log.d(TAG, "query: Matched CODE_DEVICE_ID, creating cursor")
                    // Create a cursor with device ID information
                    val cursor = MatrixCursor(arrayOf(
                        COLUMN_DEVICE_ID,
                        COLUMN_REGISTERED,
                        COLUMN_TIMESTAMP,
                        COLUMN_DEVICE_TYPE,
                        COLUMN_DEVICE_NAME
                    ))

                    // Get device information - Use Android ID as the Device ID
                    val deviceId = Settings.Secure.getString(context!!.contentResolver, Settings.Secure.ANDROID_ID) ?: ""
                    val isRegistered = preferencesManager.isDeviceRegistered()
                    val timestamp = preferencesManager.getRegistrationTimestamp()
                    val deviceType = DeviceTypeUtil.getDeviceTypeString(context!!)
                    val deviceName = android.os.Build.DEVICE

                    Log.d(TAG, "query: Device ID (Android ID): $deviceId, Registered: $isRegistered, Type: $deviceType, Name: $deviceName")

                    // Add a row with device information - Android ID is the Device ID
                    cursor.newRow()
                        .add(COLUMN_DEVICE_ID, deviceId)  // Android ID is the Device ID
                        .add(COLUMN_REGISTERED, if (isRegistered) 1 else 0)
                        .add(COLUMN_TIMESTAMP, timestamp)
                        .add(COLUMN_DEVICE_TYPE, deviceType)
                        .add(COLUMN_DEVICE_NAME, deviceName)

                    Log.d(TAG, "query: Successfully created cursor with ${cursor.count} rows")
                    cursor
                }
                else -> {
                    Log.e(TAG, "query: No match found for URI: $uri")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "query: Error processing query", e)
            return null
        }
    }

    override fun getType(uri: Uri): String? {
        Log.d(TAG, "getType: Determining type for URI: $uri")
        val match = uriMatcher.match(uri)
        Log.d(TAG, "getType: URI matcher result: $match")

        return when (match) {
            CODE_DEVICE_ID -> {
                val type = "vnd.android.cursor.item/vnd.$AUTHORITY.$PATH_DEVICE_ID"
                Log.d(TAG, "getType: Returning type: $type")
                type
            }
            else -> {
                Log.e(TAG, "getType: No match found for URI: $uri")
                null
            }
        }
    }

    // We don't support insert, update, or delete operations
    override fun insert(uri: Uri, values: ContentValues?): Uri? = null
    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<out String>?): Int = 0
    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int = 0
}
