package com.nrb.dms.provider

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.util.Log
import com.nrb.dms.utils.DeviceTypeUtil

/**
 * Helper class for third-party apps to access device registration information
 */
class DeviceRegistrationHelper(private val context: Context) {

    companion object {
        private const val TAG = "DeviceRegistrationHelper"
        private const val PERMISSION = "com.nrb.dms.permission.READ_DEVICE_ID"
    }

    /**
     * Check if the app has permission to access device ID
     * @return true (always granted since ContentProvider is now open access)
     */
    fun hasPermission(): Boolean {
        // ContentProvider is now open access since Device ID is just Android ID
        return true
    }

    /**
     * Get the device ID if available
     * @return device ID or null if not available or no permission
     */
    fun getDeviceId(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device ID")
            return null
        }

        var deviceId: String? = null

        try {
            val cursor: Cursor? = context.contentResolver.query(
                DeviceRegistrationProvider.CONTENT_URI,
                null,
                null,
                null,
                null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val idIndex = it.getColumnIndex(DeviceRegistrationProvider.COLUMN_DEVICE_ID)
                    val registeredIndex = it.getColumnIndex(DeviceRegistrationProvider.COLUMN_REGISTERED)

                    if (idIndex != -1 && registeredIndex != -1) {
                        val isRegistered = it.getInt(registeredIndex) == 1

                        if (isRegistered) {
                            deviceId = it.getString(idIndex)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing device ID", e)
        }

        return deviceId
    }

    /**
     * Check if the device is registered
     * @return true if registered, false otherwise
     */
    fun isDeviceRegistered(): Boolean {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device registration status")
            return false
        }

        var isRegistered = false

        try {
            val cursor: Cursor? = context.contentResolver.query(
                DeviceRegistrationProvider.CONTENT_URI,
                null,
                null,
                null,
                null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val registeredIndex = it.getColumnIndex(DeviceRegistrationProvider.COLUMN_REGISTERED)

                    if (registeredIndex != -1) {
                        isRegistered = it.getInt(registeredIndex) == 1
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing device registration status", e)
        }

        return isRegistered
    }

    /**
     * Get the registration timestamp
     * @return timestamp in milliseconds or 0 if not registered or no permission
     */
    fun getRegistrationTimestamp(): Long {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access registration timestamp")
            return 0
        }

        var timestamp = 0L

        try {
            val cursor: Cursor? = context.contentResolver.query(
                DeviceRegistrationProvider.CONTENT_URI,
                null,
                null,
                null,
                null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val timestampIndex = it.getColumnIndex(DeviceRegistrationProvider.COLUMN_TIMESTAMP)

                    if (timestampIndex != -1) {
                        timestamp = it.getLong(timestampIndex)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing registration timestamp", e)
        }

        return timestamp
    }

    /**
     * Get the device type (phone, tablet, foldable)
     * @return device type string or null if not available or no permission
     */
    fun getDeviceType(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device type")
            return null
        }

        var deviceType: String? = null

        try {
            val cursor: Cursor? = context.contentResolver.query(
                DeviceRegistrationProvider.CONTENT_URI,
                null,
                null,
                null,
                null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val deviceTypeIndex = it.getColumnIndex(DeviceRegistrationProvider.COLUMN_DEVICE_TYPE)

                    if (deviceTypeIndex != -1) {
                        deviceType = it.getString(deviceTypeIndex)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing device type", e)
        }

        return deviceType
    }

    /**
     * Get the device name
     * @return device name string or null if not available or no permission
     */
    fun getDeviceName(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device name")
            return null
        }

        var deviceName: String? = null

        try {
            val cursor: Cursor? = context.contentResolver.query(
                DeviceRegistrationProvider.CONTENT_URI,
                null,
                null,
                null,
                null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val deviceNameIndex = it.getColumnIndex(DeviceRegistrationProvider.COLUMN_DEVICE_NAME)

                    if (deviceNameIndex != -1) {
                        deviceName = it.getString(deviceNameIndex)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing device name", e)
        }

        return deviceName
    }

    /**
     * Get the Android ID (same as Device ID)
     * @return Android ID string or null if not available or no permission
     */
    fun getAndroidId(): String? {
        // Android ID is the same as Device ID in our system
        return getDeviceId()
    }
}
