package com.nrb.dms.provider

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.util.Log
import com.nrb.dms.database.DataContract
import com.nrb.dms.manager.DeviceDataManager

/**
 * Helper class for third-party apps to access device registration information
 * via the new DataContentProvider system
 */
class DeviceRegistrationHelper(private val context: Context) {

    companion object {
        private const val TAG = "DeviceRegistrationHelper"
    }

    /**
     * Check if the app has permission to access device ID
     * @return true (always granted since ContentProvider is now open access)
     */
    fun hasPermission(): Boolean {
        // ContentProvider is open access since Device ID is just Android ID
        return true
    }

    /**
     * Get the device ID (Android ID) from the DataContentProvider
     * @return Device ID if registered and available, null otherwise
     */
    fun getDeviceId(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device ID")
            return null
        }

        return getValueForKey(DeviceDataManager.KEY_DEVICE_ID)
    }

    /**
     * Check if the device is registered
     * @return true if registered, false otherwise
     */
    fun isDeviceRegistered(): Boolean {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device registration status")
            return false
        }

        val registeredValue = getValueForKey(DeviceDataManager.KEY_REGISTERED)
        return registeredValue?.equals("true", ignoreCase = true) == true
    }

    /**
     * Get the registration timestamp
     * @return timestamp in milliseconds or 0 if not registered or no permission
     */
    fun getRegistrationTimestamp(): Long {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access registration timestamp")
            return 0
        }

        val timestampValue = getValueForKey(DeviceDataManager.KEY_REGISTRATION_TIMESTAMP)
        return timestampValue?.toLongOrNull() ?: 0L
    }

    /**
     * Get the device type (phone, tablet, foldable)
     * @return device type string or null if not available or no permission
     */
    fun getDeviceType(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device type")
            return null
        }

        return getValueForKey(DeviceDataManager.KEY_DEVICE_TYPE)
    }

    /**
     * Get the device name
     * @return device name string or null if not available or no permission
     */
    fun getDeviceName(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device name")
            return null
        }

        return getValueForKey(DeviceDataManager.KEY_DEVICE_NAME)
    }

    /**
     * Get the device model
     * @return device model string or null if not available or no permission
     */
    fun getDeviceModel(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device model")
            return null
        }

        return getValueForKey(DeviceDataManager.KEY_DEVICE_MODEL)
    }

    /**
     * Get the device manufacturer
     * @return manufacturer string or null if not available or no permission
     */
    fun getManufacturer(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access manufacturer")
            return null
        }

        return getValueForKey(DeviceDataManager.KEY_MANUFACTURER)
    }

    /**
     * Get the Android version
     * @return Android version string or null if not available or no permission
     */
    fun getAndroidVersion(): String? {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access Android version")
            return null
        }

        return getValueForKey(DeviceDataManager.KEY_ANDROID_VERSION)
    }

    /**
     * Get the API level
     * @return API level or 0 if not available or no permission
     */
    fun getApiLevel(): Int {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access API level")
            return 0
        }

        val apiLevelValue = getValueForKey(DeviceDataManager.KEY_API_LEVEL)
        return apiLevelValue?.toIntOrNull() ?: 0
    }

    /**
     * Get all device registration data as a map
     * @return Map of all available device data, empty if no permission
     */
    fun getAllDeviceData(): Map<String, String> {
        if (!hasPermission()) {
            Log.e(TAG, "App does not have permission to access device data")
            return emptyMap()
        }

        val deviceData = mutableMapOf<String, String>()

        try {
            val cursor = context.contentResolver.query(
                Uri.parse(DataContract.DataEntry.CONTENT_URI),
                null, null, null, null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val keyIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_KEY)
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)

                    if (keyIndex >= 0 && valueIndex >= 0) {
                        do {
                            val key = it.getString(keyIndex)
                            val value = it.getString(valueIndex)
                            
                            // Only include device-related keys
                            if (isDeviceRelatedKey(key)) {
                                deviceData[key] = value
                            }
                        } while (it.moveToNext())
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing all device data", e)
        }

        return deviceData
    }

    /**
     * Get a value for a specific key from the DataContentProvider
     * @param key The key to query
     * @return The value if found, null otherwise
     */
    private fun getValueForKey(key: String): String? {
        try {
            val uri = Uri.parse("${DataContract.DataEntry.CONTENT_URI}/$key")
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                if (it.moveToFirst()) {
                    val valueIndex = it.getColumnIndex(DataContract.DataEntry.COLUMN_VALUE)
                    if (valueIndex >= 0) {
                        return it.getString(valueIndex)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing value for key: $key", e)
        }

        return null
    }

    /**
     * Check if a key is device-related
     * @param key The key to check
     * @return true if the key is device-related
     */
    private fun isDeviceRelatedKey(key: String): Boolean {
        return key.startsWith("device_") || 
               key == DeviceDataManager.KEY_REGISTERED ||
               key == DeviceDataManager.KEY_REGISTRATION_TIMESTAMP ||
               key == DeviceDataManager.KEY_MANUFACTURER ||
               key == DeviceDataManager.KEY_ANDROID_VERSION ||
               key == DeviceDataManager.KEY_API_LEVEL ||
               key == DeviceDataManager.KEY_BRAND ||
               key == DeviceDataManager.KEY_PRODUCT ||
               key == DeviceDataManager.KEY_HARDWARE ||
               key == DeviceDataManager.KEY_FINGERPRINT ||
               key == DeviceDataManager.KEY_LOCALE ||
               key == DeviceDataManager.KEY_TIMEZONE
    }
}
