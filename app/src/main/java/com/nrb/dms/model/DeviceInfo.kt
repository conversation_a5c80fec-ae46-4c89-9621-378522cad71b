package com.nrb.dms.model

import com.google.gson.annotations.SerializedName

/**
 * Data class to hold device information for registration
 */
data class DeviceInfo(
    @SerializedName("deviceModel")
    val deviceModel: String,

    @SerializedName("type")
    val deviceType: String,

    @SerializedName("manufacturer")
    val manufacturer: String,

    @SerializedName("androidVersion")
    val androidVersion: String,

    @SerializedName("apiVersion")
    val apiLevel: Int,

    @SerializedName("brand")
    val brand: String,

    @SerializedName("product")
    val product: String,

    @SerializedName("hardware")
    val hardware: String,

    @SerializedName("fingerprint")
    val fingerprint: String,

    @SerializedName("locale")
    val locale: String,

    @SerializedName("timeZone")
    val timeZone: String,

    @SerializedName("androidId")
    val androidId: String,

    @SerializedName("name")
    val deviceName: String,

    @SerializedName("registrationTimestamp")
    val registrationTimestamp: Long = 0,

    @SerializedName("isActive")
    val isActive: Boolean = true
)

/**
 * Response from the device registration API
 */
data class DeviceRegistrationResponse(
    @SerializedName("success")
    val success: Boolean,

    @SerializedName("message")
    val message: String,

    @SerializedName("id")
    val deviceId: String?,

    @SerializedName("data")
    val data: DeviceInfo?
)
