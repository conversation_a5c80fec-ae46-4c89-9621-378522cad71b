<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/text_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:textAlignment="center"
        android:textSize="24sp"
        android:textStyle="bold"
        android:text="DMS Device Management"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Device ID Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/device_id_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_main">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:id="@+id/device_id_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/device_id_label"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/device_id_label_color"
                android:layout_gravity="center_horizontal" />

            <TextView
                android:id="@+id/device_id_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/device_id_text_color"
                android:fontFamily="monospace"
                android:textAlignment="center"
                android:layout_gravity="center_horizontal"
                android:selectAllOnFocus="true"
                android:background="@drawable/device_id_background"
                android:padding="12dp"
                tools:text="DMS-12345-ABCDE" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:layout_gravity="center_horizontal">

                <Button
                    android:id="@+id/copy_device_id_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="@string/copy_device_id"
                    android:textSize="12sp"
                    android:backgroundTint="@color/copy_button_color"
                    android:textColor="@android:color/white"
                    android:drawableStart="@android:drawable/ic_menu_share"
                    android:drawablePadding="4dp"
                    android:layout_marginEnd="8dp"
                    style="?android:attr/buttonStyleSmall" />

                <Button
                    android:id="@+id/refresh_device_id_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="Refresh"
                    android:textSize="12sp"
                    android:backgroundTint="@color/status_success"
                    android:textColor="@android:color/white"
                    android:drawableStart="@android:drawable/ic_menu_rotate"
                    android:drawablePadding="4dp"
                    style="?android:attr/buttonStyleSmall" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Status Information -->
    <TextView
        android:id="@+id/status_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:textAlignment="center"
        android:textSize="14sp"
        android:textColor="@color/device_id_label_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/device_id_card"
        tools:text="Device registered on: May 25, 2023 10:30 AM" />

</androidx.constraintlayout.widget.ConstraintLayout>
