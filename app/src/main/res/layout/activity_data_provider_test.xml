<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Data Provider Test"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Input Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Key:"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/editTextKey"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Enter key"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Value:"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/editTextValue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Enter value"
        android:layout_marginBottom="24dp" />

    <!-- Button Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/buttonInsert"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Insert"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/buttonQuery"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Query"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/buttonUpdate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Update"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/buttonDelete"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Delete"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <Button
        android:id="@+id/buttonQueryAll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Query All Data"
        android:layout_marginBottom="24dp" />

    <!-- Results Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Results:"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/textViewResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No data loaded yet"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:background="@android:color/darker_gray"
            android:padding="12dp"
            android:textColor="@android:color/white" />

    </ScrollView>

</LinearLayout>
