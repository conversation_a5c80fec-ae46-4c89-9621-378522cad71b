<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".StatusActivity">

    <ImageView
        android:id="@+id/status_icon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:contentDescription="@string/status_icon_description"
        app:layout_constraintBottom_toTopOf="@+id/status_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:textAlignment="center"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/timestamp_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/status_icon"
        tools:text="Device Registration Status" />

    <TextView
        android:id="@+id/timestamp_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textAlignment="center"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/device_id_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/status_text"
        tools:text="Registered on: May 25, 2023 10:30 AM" />

    <!-- Device ID Container -->
    <LinearLayout
        android:id="@+id/device_id_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        android:background="@drawable/device_id_background"
        android:padding="16dp"
        app:layout_constraintBottom_toTopOf="@+id/server_url_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timestamp_text">

        <TextView
            android:id="@+id/device_id_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/device_id_label"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/device_id_label_color"
            android:layout_gravity="center_horizontal" />

        <TextView
            android:id="@+id/device_id_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/device_id_text_color"
            android:fontFamily="monospace"
            android:textAlignment="center"
            android:layout_gravity="center_horizontal"
            android:selectAllOnFocus="true"
            tools:text="DMS-12345-ABCDE" />

        <Button
            android:id="@+id/copy_device_id_button"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginTop="8dp"
            android:text="@string/copy_device_id"
            android:textSize="12sp"
            android:layout_gravity="center_horizontal"
            android:backgroundTint="@color/copy_button_color"
            android:textColor="@android:color/white"
            android:drawableStart="@android:drawable/ic_menu_share"
            android:drawablePadding="4dp"
            style="?android:attr/buttonStyleSmall" />

    </LinearLayout>

    <TextView
        android:id="@+id/server_url_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textAlignment="center"
        android:textSize="14sp"
        android:textStyle="italic"
        app:layout_constraintBottom_toTopOf="@+id/retry_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timestamp_text"
        tools:text="Server: https://example.com/" />

    <Button
        android:id="@+id/retry_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/retry_registration"
        android:visibility="gone"
        android:backgroundTint="@color/status_success"
        app:layout_constraintBottom_toTopOf="@+id/settings_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timestamp_text"
        tools:visibility="visible" />

    <Button
        android:id="@+id/settings_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="32dp"
        android:text="@string/server_settings"
        android:drawableStart="@android:drawable/ic_menu_manage"
        android:drawablePadding="8dp"
        android:backgroundTint="@color/purple_500"
        android:textColor="@android:color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/retry_button" />

</androidx.constraintlayout.widget.ConstraintLayout>
